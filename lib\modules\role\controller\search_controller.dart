import 'package:get/get.dart';
import 'package:rolio/common/models/ai_role.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/toast_util.dart';
import 'package:rolio/modules/role/repository/search_repository.dart';
import 'package:rolio/modules/role/service/search_service.dart';
import 'package:flutter/material.dart';
import 'dart:async';

/// 搜索控制器
/// 
/// 负责管理搜索页面的UI状态和业务逻辑
class SearchController extends GetxController {
  // 服务
  late final SearchService searchService;
  
  // 搜索关键词
  final RxString searchKeyword = ''.obs;
  
  // 搜索结果
  final RxList<AiRole> searchResults = <AiRole>[].obs;
  
  // 加载状态
  final RxBool isSearching = false.obs;
  final RxBool isLoadingMore = false.obs;
  final RxBool isWaitingSuggestions = false.obs; // 等待搜索建议的状态
  
  // 搜索历史
  final RxList<SearchRecord> searchHistory = <SearchRecord>[].obs;
  
  // 搜索建议
  final RxList<String> searchSuggestions = <String>[].obs;
  
  // 随机角色名(用作提示)
  final RxString randomRoleName = ''.obs;
  
  // 编辑模式(删除历史记录)
  final RxBool isEditMode = false.obs;
  
  // 分页信息
  final RxInt currentPage = 1.obs;
  final RxInt totalPages = 1.obs;
  final RxInt totalItems = 0.obs;
  
  // 滚动控制器
  final ScrollController scrollController = ScrollController();
  
  // 搜索框控制器
  final TextEditingController textEditingController = TextEditingController();
  
  // 搜索框焦点
  final FocusNode searchFocusNode = FocusNode();
  
  // 搜索建议延迟定时器
  Timer? _suggestionsTimer;

  @override
  void onInit() {
    super.onInit();
    searchService = Get.find<SearchService>();
    
    // 加载搜索历史
    _loadSearchHistory();
    
    // 加载随机角色名
    _loadRandomRoleName();
    
    // 监听滚动，实现滚动到底部自动加载更多
    scrollController.addListener(_scrollListener);
    
    // 监听搜索框文本变化
    textEditingController.addListener(_onSearchTextChanged);
  }
  
  @override
  void onClose() {
    // 释放资源
    scrollController.removeListener(_scrollListener);
    scrollController.dispose();
    textEditingController.removeListener(_onSearchTextChanged);
    textEditingController.dispose();
    searchFocusNode.dispose();
    searchService.cancelDebounce();
    _suggestionsTimer?.cancel();
    super.onClose();
  }
  
  /// 监听搜索框文本变化
  void _onSearchTextChanged() {
    final text = textEditingController.text;
    
    // 更新关键词
    searchKeyword.value = text;
    
    // 如果文本为空，清除搜索结果和建议
    if (text.isEmpty) {
      searchResults.clear();
      searchSuggestions.clear();
      isWaitingSuggestions.value = false;
      _suggestionsTimer?.cancel();
      return;
    }
    
    // 设置等待搜索建议状态为true
    isWaitingSuggestions.value = true;
    
    // 取消之前的定时器
    _suggestionsTimer?.cancel();
    
    // 创建新的定时器，延迟1秒后如果还没有建议就显示"No results found"
    _suggestionsTimer = Timer(const Duration(seconds: 1), () {
      isWaitingSuggestions.value = false;
    });
    
    // 获取搜索建议
    searchService.getSearchSuggestions(text, callback: (suggestions) {
      // 如果用户已经继续输入，不更新建议
      if (textEditingController.text != text) {
        return;
      }
      
      searchSuggestions.value = suggestions;
      isWaitingSuggestions.value = false; // 已获取建议，不再等待
      _suggestionsTimer?.cancel(); // 取消定时器
    });
  }
  
  /// 监听滚动事件
  void _scrollListener() {
    // 如果已经到达底部，且不是正在加载中，则加载更多
    if (scrollController.position.pixels >= scrollController.position.maxScrollExtent - 200 &&
        !isSearching.value &&
        !isLoadingMore.value &&
        currentPage.value < totalPages.value) {
      loadMore();
    }
  }
  
  /// 执行搜索
  Future<void> search(String keyword) async {
    if (keyword.trim().isEmpty) {
      // 如果关键词为空但有随机角色名，则搜索随机角色
      if (randomRoleName.isNotEmpty) {
        keyword = randomRoleName.value;
        textEditingController.text = keyword;
      } else {
        return;
      }
    }
    
    try {
      isSearching.value = true;
      searchKeyword.value = keyword;
      
      LogUtil.debug('搜索角色: $keyword');
      
      final result = await searchService.searchRoles(
        keyword: keyword,
        page: 1,
        pageSize: 20,
      );
      
      // 更新搜索结果
      searchResults.clear();
      if (result['items'] is List) {
        searchResults.addAll(result['items'] as List<AiRole>);
      }
      
      // 更新分页信息
      currentPage.value = result['page'] ?? 1;
      totalPages.value = result['pages'] ?? 1;
      totalItems.value = result['total'] ?? 0;
      
      LogUtil.debug('搜索结果: ${searchResults.length}个角色, 总共: ${totalItems.value}个');
      
      // 退出编辑模式
      isEditMode.value = false;
      
      // 隐藏搜索建议
      searchSuggestions.clear();
      isWaitingSuggestions.value = false;
      
      // 重新加载搜索历史
      _loadSearchHistory();
    } catch (e) {
      LogUtil.error('搜索角色失败: $e');
      ToastUtil.error('Failed to search roles');
    } finally {
      isSearching.value = false;
    }
  }
  
  /// 加载更多搜索结果
  Future<void> loadMore() async {
    if (isSearching.value || isLoadingMore.value || searchKeyword.isEmpty) {
      return;
    }
    
    try {
      isLoadingMore.value = true;
      
      final currentResultMap = {
        'items': searchResults.toList(),
        'page': currentPage.value,
        'pages': totalPages.value,
        'total': totalItems.value,
        'size': 20,
      };
      
      LogUtil.debug('加载更多搜索结果，当前页: ${currentPage.value}');
      
      final result = await searchService.loadNextPage(
        currentResultMap,
        keyword: searchKeyword.value,
      );
      
      // 更新搜索结果
      searchResults.clear();
      if (result['items'] is List) {
        searchResults.addAll(result['items'] as List<AiRole>);
      }
      
      // 更新分页信息
      currentPage.value = result['page'] ?? currentPage.value;
      totalPages.value = result['pages'] ?? totalPages.value;
      totalItems.value = result['total'] ?? totalItems.value;
      
      LogUtil.debug('加载更多搜索结果完成，当前结果数: ${searchResults.length}');
    } catch (e) {
      LogUtil.error('加载更多搜索结果失败: $e');
    } finally {
      isLoadingMore.value = false;
    }
  }
  
  /// 清除搜索结果
  void clearSearch() {
    searchKeyword.value = '';
    textEditingController.clear();
    searchResults.clear();
    searchSuggestions.clear();
    isWaitingSuggestions.value = false;
    currentPage.value = 1;
    totalPages.value = 1;
    totalItems.value = 0;
    _suggestionsTimer?.cancel();
    
    // 退出编辑模式
    isEditMode.value = false;
    
    // 重新加载随机角色名
    _loadRandomRoleName();
  }
  
  /// 从搜索历史中执行搜索
  void searchFromHistory(SearchRecord record) {
    textEditingController.text = record.keyword;
    search(record.keyword);
  }
  
  /// 从搜索建议中执行搜索
  void searchFromSuggestion(String suggestion) {
    textEditingController.text = suggestion;
    search(suggestion);
  }
  
  /// 切换编辑模式
  void toggleEditMode() {
    isEditMode.value = !isEditMode.value;
  }
  
  /// 删除单个搜索历史
  Future<void> deleteSearchRecord(int recordId) async {
    try {
      final success = await searchService.deleteSearchRecord(recordId);
      if (success) {
        // 移除本地列表中的记录
        searchHistory.removeWhere((record) => record.id == recordId);
      } else {
        LogUtil.warn('删除搜索记录失败: $recordId');
        ToastUtil.error('Failed to delete search history');
      }
    } catch (e) {
      LogUtil.error('删除搜索记录失败: $e');
    }
  }
  
  /// 清除所有搜索历史
  Future<void> clearAllSearchHistory() async {
    try {
      final deletedCount = await searchService.deleteAllSearchRecords();
      LogUtil.debug('删除搜索历史: $deletedCount 条记录');
      
      // 清空本地列表
      searchHistory.clear();
      
      // 退出编辑模式
      isEditMode.value = false;
      
      ToastUtil.success('All search history cleared');
    } catch (e) {
      LogUtil.error('清除所有搜索历史失败: $e');
      ToastUtil.error('Failed to clear search history');
    }
  }
  
  /// 加载搜索历史
  Future<void> _loadSearchHistory() async {
    try {
      final histories = await searchService.getSearchHistory(size: 20, forceRefresh: true);
      searchHistory.value = histories;
    } catch (e) {
      LogUtil.error('加载搜索历史失败: $e');
    }
  }
  
  /// 加载随机角色名
  Future<void> _loadRandomRoleName() async {
    try {
      final name = await searchService.getRandomRoleName();
      randomRoleName.value = name;
    } catch (e) {
      LogUtil.error('加载随机角色名失败: $e');
    }
  }
} 