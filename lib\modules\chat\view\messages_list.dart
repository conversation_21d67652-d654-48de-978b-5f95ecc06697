import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:rolio/common/constants/role_constants.dart';
import 'package:rolio/common/enums/message_type.dart';
import 'package:rolio/common/enums/swipe_direction.dart';
import 'package:rolio/common/utils/datetime_utils.dart';
import 'package:rolio/common/utils/image_preloader.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/interfaces/chat_service_interface.dart';
import 'package:rolio/common/interfaces/role_provider.dart';
import 'package:rolio/manager/global_state.dart';
import 'package:rolio/modules/chat/controller/chat_controller.dart';
import 'package:rolio/modules/chat/model/message.dart';
import 'package:rolio/modules/chat/view/message_card.dart';
import 'package:rolio/modules/chat/view/virtualized_message_list.dart';
import 'package:rolio/modules/chat/view/chat_status_widgets.dart';
import 'package:rolio/modules/chat/view/ai_typing_bubble.dart';
import 'package:rolio/modules/chat/view/system_message_card.dart';

class MessagesList extends StatefulWidget {
  final IChatService? chatService;
  final IRoleProvider? roleProvider;
  final GlobalState? globalState;
  final ChatController? chatController;
  
  const MessagesList({
    super.key,
    this.chatService,
    this.roleProvider,
    this.globalState,
    this.chatController,
  });

  @override
  State<MessagesList> createState() => _MessageListState();
}

class _MessageListState extends State<MessagesList> with AutomaticKeepAliveClientMixin {
  late final ScrollController _messagesScrollController;
  late final ChatController _chatController;
  
  // 聊天服务
  late final IChatService _chatService;
  
  // 角色提供者服务，用于获取AI头像
  late final IRoleProvider _roleProvider;
  
  // 全局状态
  late final GlobalState _globalState;
  
  // 添加一个计数器，用于检测消息列表长度变化
  int _previousMessageCount = 0;
  
  // 添加一个标志，表示是否是第一次加载
  bool _isFirstLoad = true;
  
  // 添加一个标志，表示是否是通过下拉刷新加载的
  bool _isLoadingViaRefresh = false;
  
  // 保存AI角色的头像URL - 使用LRU缓存限制大小
  final Map<int, String?> _aiAvatarCache = {};
  final int _maxCacheSize = 50; // 限制缓存大小
  final List<int> _cacheAccessOrder = []; // 记录访问顺序
  
  // 图片预加载器 - 使用单例模式
  final ImagePreloader _imagePreloader = ImagePreloader();
  
  // 防止重复加载标志
  bool _isLoadingMore = false;
  
  // 添加防抖计时器，避免频繁触发加载
  Timer? _loadMoreDebounce;
  
  // 估计的消息高度，用于优化ListView性能
  final double _estimatedMessageHeight = 80.0;
  
  // 预加载的消息头像
  final Map<String, Future<String?>> _avatarFutures = {};
  
  // 使用PageStorageKey保持滚动位置
  final PageStorageKey _pageStorageKey = const PageStorageKey('chat_messages_list');

  @override
  void initState() {
    super.initState();
    _messagesScrollController = ScrollController();
    
    // 使用依赖注入或回退到Get.find
    _chatController = widget.chatController ?? Get.find<ChatController>();
    _chatService = widget.chatService ?? Get.find<IChatService>();
    _roleProvider = widget.roleProvider ?? Get.find<IRoleProvider>();
    _globalState = widget.globalState ?? Get.find<GlobalState>();
    
    // 使用延迟添加滚动监听，避免初始化时过早触发
    Future.delayed(Duration(milliseconds: 300), () {
      // 添加滚动监听
      if (_messagesScrollController.hasClients) {
        _messagesScrollController.addListener(_scrollListener);
      }
    });
    
    // 预加载当前角色头像
    _preloadCurrentRoleAvatar();
  }

  @override
  void dispose() {
    _messagesScrollController.removeListener(_scrollListener);
    _messagesScrollController.dispose();
    _loadMoreDebounce?.cancel();
    super.dispose();
  }
  
  // 预加载当前角色头像
  void _preloadCurrentRoleAvatar() {
    final roleId = _chatService.currentAiRoleId;
    if (roleId > 0) {
      final senderUserId = '${RoleConstants.AI_SENDER_PREFIX}$roleId';
      _getAiAvatarUrl(senderUserId);
    }
  }
  
  // 收起键盘方法
  void _hideKeyboard() {
    // 使用SystemChannels隐藏键盘
    SystemChannels.textInput.invokeMethod('TextInput.hide');
    // 同时取消焦点
    FocusScope.of(context).unfocus();
  }
  
  // 滚动监听函数 - 简化滚动逻辑
  void _scrollListener() {
    // 滚动时隐藏键盘
    _hideKeyboard();
    
    // 不再自动触发加载更多，避免滚动位置重置问题
    // 用户需要通过下拉刷新或专用按钮来加载更多历史消息
    
    // 检查是否需要显示回到底部按钮
    final showButton = _messagesScrollController.position.pixels > 300;
    if (_showScrollToBottomButton.value != showButton) {
      _showScrollToBottomButton.value = showButton;
    }
  }
  
  // 触发加载更多 - 使用防抖减少频繁触发
  void _triggerLoadMore() {
    // 取消之前的定时器
    _loadMoreDebounce?.cancel();
    
    // 设置新的定时器
    _loadMoreDebounce = Timer(const Duration(milliseconds: 200), () {
      if (!_isLoadingMore && _chatService.hasMoreMessages) {
        // 使用专门的加载更多历史消息方法，避免页码重置问题
        _chatService.loadMoreHistoryMessages(_chatService.currentConversationId);
      }
    });
  }
  
  // 加载更多历史消息
  Future<void> _loadMoreMessages() async {
    if (_chatService.isLoadingMore.value || !_chatService.hasMoreMessages || _isLoadingMore) {
      LogUtil.debug('跳过加载历史消息: 正在加载=${_chatService.isLoadingMore.value}, 还有更多消息=${_chatService.hasMoreMessages}');
      return;
    }
    
    // 获取当前会话ID
    final conversationId = _chatService.currentConversationId;
    if (conversationId <= 0) {
      LogUtil.warn('无法加载更多消息：无效的会话ID');
      return;
    }
    
    // 标记开始加载更多
    _isLoadingMore = true;
    _chatService.isLoadingMore.value = true;
    
    try {
      // 记录当前滚动位置和内容高度
      final double previousScrollOffset = _messagesScrollController.position.pixels;
      final double previousContentHeight = _messagesScrollController.position.viewportDimension + previousScrollOffset;
      
      // 记录当前消息数量
      final int oldMessageCount = _chatService.messages.length;
      
      // 使用专门的加载更多历史消息方法，避免页码重置问题
      await _chatService.loadMoreHistoryMessages(conversationId);
      
      // 加载完成后调整滚动位置
      if (_chatService.messages.length > oldMessageCount) {
        // 计算新的内容高度
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_messagesScrollController.hasClients) {
            final double newContentHeight = _messagesScrollController.position.viewportDimension + _messagesScrollController.position.maxScrollExtent;
            final double contentHeightDifference = newContentHeight - previousContentHeight;
            
            // 调整滚动位置，保持用户看到的内容不变
            _messagesScrollController.jumpTo(previousScrollOffset + contentHeightDifference);
            
            LogUtil.debug('加载更多后调整滚动位置: 原位置=$previousScrollOffset, 新位置=${previousScrollOffset + contentHeightDifference}');
          }
        });
      }
    } catch (e) {
      LogUtil.error('加载更多历史消息失败: $e');
      // 错误已在服务层处理
    } finally {
      _isLoadingMore = false;
    }
  }
  
  // 获取AI角色头像URL - 使用缓存优化
  Future<String?> _getAiAvatarUrl(String senderUserId) async {
    // 检查是否已有该发送者的头像请求正在进行中
    if (_avatarFutures.containsKey(senderUserId)) {
      return _avatarFutures[senderUserId];
    }
    
    // 创建新的Future并缓存
    final avatarFuture = _fetchAvatarUrl(senderUserId);
    _avatarFutures[senderUserId] = avatarFuture;
    
    return avatarFuture;
  }
  
  // 实际获取头像的方法
  Future<String?> _fetchAvatarUrl(String senderUserId) async {
    // 安全检查
    if (senderUserId.isEmpty) {
      return '';
    }
    
    if (!senderUserId.startsWith(RoleConstants.AI_SENDER_PREFIX)) {
      return null;
    }
    
    try {
      // 从发送者ID中提取角色ID
      final roleIdStr = senderUserId.length > RoleConstants.AI_SENDER_PREFIX.length 
          ? senderUserId.substring(RoleConstants.AI_SENDER_PREFIX.length) 
          : 'unknown';
      
      // 尝试获取角色ID
      int? roleId;
      
      // 对于"unknown"情况，使用默认角色ID或尝试从当前会话获取
      if (roleIdStr == 'unknown' || !isNumeric(roleIdStr)) {
        roleId = _chatService.currentAiRoleId;
        
        // 如果没有活跃角色ID，尝试从控制器获取
        if (roleId <= 0) {
          roleId = _chatController.aiRoleId;
        }
        
        // 如果仍然无效，使用默认AI角色ID
        if (roleId <= 0) {
          roleId = RoleConstants.DEFAULT_AI_ROLE_ID;
        }
      } else {
        roleId = int.tryParse(roleIdStr);
        if (roleId == null) {
          return '';
        }
      }
      
      // 更新缓存访问顺序
      _updateCacheOrder(roleId);
      
      // 如果缓存中有，直接返回
      if (_aiAvatarCache.containsKey(roleId)) {
        final cachedUrl = _aiAvatarCache[roleId];
        
        // 如果URL有效，使用图片预加载器预加载
        if (cachedUrl != null && cachedUrl.isNotEmpty) {
          if (!_imagePreloader.isImagePreloaded(cachedUrl)) {
            _imagePreloader.preloadImage(cachedUrl);
          }
        }
        
        return cachedUrl;
      }
      
      // 获取并缓存头像URL
      String? avatarUrl;
      avatarUrl = (await _roleProvider.getAvatarUrlById(roleId))?.trim().replaceAll(RegExp(r'^`+|`+$'), '');
          
      if (avatarUrl == null || avatarUrl.isEmpty) {
        _aiAvatarCache[roleId] = '';
        return _aiAvatarCache[roleId];
      }
      
      // 检查缓存大小，如果超过限制则移除最久未使用的项
      _checkCacheSize();
      
      _aiAvatarCache[roleId] = avatarUrl;
      
      // 预加载图片
      if (!_imagePreloader.isImagePreloaded(avatarUrl)) {
        _imagePreloader.preloadImage(avatarUrl);
      }
      
      return avatarUrl;
    } catch (e) {
      return '';
    }
  }
  
  // 更新缓存访问顺序
  void _updateCacheOrder(int roleId) {
    // 如果已存在，先移除旧的位置
    _cacheAccessOrder.remove(roleId);
    // 添加到最新访问位置
    _cacheAccessOrder.add(roleId);
  }
  
  // 检查缓存大小，如果超过限制则移除最久未使用的项
  void _checkCacheSize() {
    while (_aiAvatarCache.length >= _maxCacheSize && _cacheAccessOrder.isNotEmpty) {
      final oldestRoleId = _cacheAccessOrder.removeAt(0);
      _aiAvatarCache.remove(oldestRoleId);
      LogUtil.debug('移除最久未使用的AI头像缓存: roleId=$oldestRoleId');
    }
  }
  
  // 检查字符串是否为数字
  bool isNumeric(String? str) {
    if (str == null) return false;
    return int.tryParse(str) != null;
  }
  
  // 处理消息滑动
  void _handleMessageSwipe(SwipeDirection direction) {
    // 根据滑动方向切换角色
    if (direction == SwipeDirection.right) {
      LogUtil.info('向右滑动，切换到上一个角色');
      _chatController.switchRole(isNext: false);
    } else if (direction == SwipeDirection.left) {
      LogUtil.info('向左滑动，切换到下一个角色');
      _chatController.switchRole(isNext: true);
    }
  }
  
  // 滚动到底部
  void _scrollToBottom() {
    if (_messagesScrollController.hasClients) {
      _messagesScrollController.animateTo(
        0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    }
  }
  
  // 预先获取所有可见消息的头像
  void _prefetchVisibleAvatars(List<Message> messages) {
    // 只预取前10条消息的头像，避免过多请求
    final visibleMessages = messages.take(10).toList();
    
    for (final message in visibleMessages) {
      if (!message.senderUserId.startsWith(RoleConstants.AI_SENDER_PREFIX)) continue;
      
      // 触发头像预加载
      _getAiAvatarUrl(message.senderUserId);
    }
  }
  
  // 从Message对象构建MessageCard
  Widget _buildMessageCard(BuildContext context, Message message) {
    // 处理系统消息
    if (message.messageType == MessageType.system) {
      // 导入SystemMessageCard
      return SystemMessageCard(
        message: message.lastMessage,
        // 检查消息内容是否包含"Intro"，如果包含则标记为简介
        isIntro: message.lastMessage.contains("Intro"),
      );
    }
    
    final bool isSender = !message.senderUserId.startsWith(RoleConstants.AI_SENDER_PREFIX);
    
    // 如果是发送者(用户)，不需要头像
    if (isSender) {
      return MessageCard(
        message: message.lastMessage,
        isSender: true,
        time: DateTimeUtils.getMessageTimeDisplay(message.time),
        messageType: message.messageType,
        avatarUrl: null,
        onSwipe: _handleMessageSwipe,
        repliedText: message.repliedMessage ?? '',
        username: "用户",
        repliedMessageType: message.repliedMessageType ?? MessageType.text,
        swipeDirection: SwipeDirection.right,
        isSeen: message.isSeen,
        messageStatus: message.status,
      );
    }
    
    // 直接使用控制器中的头像URL，确保和AppBar一致
    final avatarUrl = _chatController.aiAvatarUrl.value;
    
    return MessageCard(
      message: message.lastMessage,
      isSender: false,
      time: DateTimeUtils.getMessageTimeDisplay(message.time),
      messageType: message.messageType,
      avatarUrl: avatarUrl,
      onSwipe: _handleMessageSwipe,
      repliedText: message.repliedMessage ?? '',
      username: _chatController.aiRoleName.value,
      repliedMessageType: message.repliedMessageType ?? MessageType.text,
      swipeDirection: SwipeDirection.right,
      isSeen: message.isSeen,
      messageStatus: message.status,
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // 必须调用super.build
    
    return Obx(() {
      final messages = _chatService.messages;
      
      // 预先获取可见消息的头像
      _prefetchVisibleAvatars(messages);
      
      // 检测消息列表长度变化，自动滚动到底部
      if (_isFirstLoad && messages.isNotEmpty) {
        _isFirstLoad = false;
        // 延迟执行，确保布局完成
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToBottom();
        });
      } else if (_chatService.isAiReplying.value && messages.length > _previousMessageCount && !_isLoadingViaRefresh) {
        // 只有当AI正在回复且消息增加时，才滚动到底部
        // 且不是通过下拉刷新加载的消息
        WidgetsBinding.instance.addPostFrameCallback((_) {
          _scrollToBottom();
        });
      }
      
      // 更新消息计数
      _previousMessageCount = messages.length;
      
      // 处理加载状态显示
      // 优先检查初始化状态，其次检查加载历史消息状态
      if (_chatController.isInitializing.value || (_chatService.isLoadingHistory && messages.isEmpty)) {
        // 显示加载视图
        return _buildSkeletonList();
      }
      
      // 使用Stack包装，添加回到底部按钮
      return Stack(
        children: [
          // 使用Container包装，确保背景透明
          Container(
            color: Colors.transparent, // 确保背景透明
            child: GestureDetector(
              // 点击空白处隐藏键盘
              onTap: _hideKeyboard,
              onHorizontalDragEnd: (details) {
                // 根据滑动速度和方向判断是向左还是向右滑动
                if (details.primaryVelocity != null) {
                  // 增加滑动阈值，必须达到一定速度和距离才触发
                  final double velocityThreshold = 300.0; // 增加速度阈值
                  
                  // 检查速度是否达到阈值
                  if (details.primaryVelocity!.abs() < velocityThreshold) {
                    return; // 如果速度不够，不触发切换
                  }
                  
                  // 检查是否正在初始化，避免重复切换
                  if (_chatController.isInitializing.value) return;
                  
                  if (details.primaryVelocity! > 0) {
                    // 向右滑动，切换到上一个角色
                    _chatController.switchToPreviousRole();
                  } else if (details.primaryVelocity! < 0) {
                    // 向左滑动，切换到下一个角色
                    _chatController.switchToNextRole();
                  }
                }
              },
              // 使用虚拟化消息列表替代ListView
              child: Column(
                children: [
                  // 消息列表
                  Expanded(
                    child: VirtualizedMessageList(
                      messages: messages,
                      controller: _messagesScrollController,
                      reverse: true,
                      padding: const EdgeInsets.all(10),
                      estimatedItemHeight: _estimatedMessageHeight,
                      isLoadingMore: _chatService.isLoadingMore.value,
                      // 添加AI回复状态
                      isAiTyping: _chatService.isAiReplying.value,
                      // 设置AI回复气泡构建器
                      aiTypingBuilder: (context) => _buildAiTypingBubble(),
                      onLoadMore: _triggerLoadMore, // 启用自动加载更多
                      onRefresh: _handleRefresh,
                      loadingBuilder: (context) => const SizedBox.shrink(), // 移除转圈动画
                      itemBuilder: (context, message) {
                        return _buildMessageCard(context, message);
                      },
                    ),
                  ),
                  
                  // 移除独立的AI回复气泡
                  // Obx(() => _chatService.isAiReplying.value ? _buildAiTypingBubble() : const SizedBox.shrink()),
                ],
              ),
            ),
          ),
          
          // 回到底部按钮
          Obx(() => _showScrollToBottomButton.value
            ? Positioned(
                right: 16,
                bottom: 16,
                child: FloatingActionButton(
                  mini: true,
                  backgroundColor: Colors.black.withAlpha(153), // 使用withAlpha替代withOpacity
                  onPressed: () {
                    _scrollToBottom();
                    // 点击回到底部按钮时也收起键盘
                    _hideKeyboard();
                  },
                  child: const Icon(
                    Icons.arrow_downward,
                    color: Colors.white,
                  ),
                ),
              )
            : const SizedBox.shrink(),
          ),
        ],
      );
    });
  }
  
  // 构建加载视图
  Widget _buildSkeletonList() {
    // 使用ChatLoadingView显示加载动画，但UI元素已经在ChatPage中更新
    return Container(
      color: Colors.transparent, // 确保背景透明
      child: const ChatLoadingView(),
    );
  }
  
  // 处理下拉刷新
  Future<void> _handleRefresh() async {
    LogUtil.debug('处理下拉刷新 - 开始加载历史消息');
    
    // 获取当前会话ID
    final conversationId = _chatService.currentConversationId;
    if (conversationId <= 0) {
      LogUtil.warn('下拉刷新无法加载消息：无效的会话ID');
      return Future.delayed(const Duration(milliseconds: 500));
    }
    
    // 设置下拉刷新标志
    _isLoadingViaRefresh = true;
    
    // 直接加载历史消息
    if (!_isLoadingMore && _chatService.hasMoreMessages) {
      LogUtil.debug('下拉刷新 - 条件满足，执行加载');
      try {
        // 使用专门的加载更多历史消息方法，避免页码重置问题
        return await _chatService.loadMoreHistoryMessages(conversationId);
      } catch (e) {
        LogUtil.error('下拉刷新加载历史消息失败: $e');
        // 错误已在服务层处理
      } finally {
        // 确保标志被重置
        _isLoadingViaRefresh = false;
      }
    } else {
      LogUtil.debug('下拉刷新 - 条件不满足: 正在加载=${_isLoadingMore}, 有更多消息=${_chatService.hasMoreMessages}');
      // 重置标志
      _isLoadingViaRefresh = false;
      // 延迟一下，让刷新指示器有时间显示
      return Future.delayed(const Duration(milliseconds: 500));
    }
  }
  
  // 添加一个RxBool来控制回到底部按钮的显示
  final RxBool _showScrollToBottomButton = false.obs;

  // 构建AI回复中气泡
  Widget _buildAiTypingBubble() {
    // 获取当前AI角色的头像
    final avatarUrl = _chatController.aiAvatarUrl.value;
    
    return Container(
      margin: const EdgeInsets.only(top: 0, bottom: 8, left: 10, right: 10),
      child: AITypingBubble(avatarUrl: avatarUrl),
    );
  }

  @override
  bool get wantKeepAlive => true; // 保持状态，避免重建
}
