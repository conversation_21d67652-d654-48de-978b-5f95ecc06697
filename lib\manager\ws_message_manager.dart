import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:get/get.dart';
import 'package:rolio/common/constants/ws_constants.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/message_tracker.dart';
import 'package:rolio/manager/ws_connection_manager.dart';
import 'package:rxdart/subjects.dart';
import 'package:rolio/env.dart';
import 'package:rolio/manager/global_state.dart';
import 'package:rolio/common/constants/error_codes.dart';
import 'package:rolio/common/utils/error_handler.dart';
import 'package:rolio/common/state/global_event_state.dart';

/// WebSocket消息结构
class WsMsg {
  final WsEvent event; // 事件类型
  final dynamic data; // 数据
  final int? error; // 错误码
  final int? conversationid; // 会话ID

  WsMsg({
    required this.event,
    required this.data,
    this.error,
    this.conversationid,
  });

  factory WsMsg.fromJson(Map<String, dynamic> json) {
    return WsMsg(
      event: WsEvent.values.firstWhere(
        (e) => e.toString() == 'WsEvent.${json['event']}',
        orElse: () => WsEvent.unknown,
      ),
      data: json['data'],
      error: json['error'] as int?,
      conversationid: json['conversation_id'] as int?,
    );
  }

  Map<String, dynamic> toJson() => {
    'event': event.toString().split('.').last,
    'data': data is Map<String, dynamic> 
        ? data
        : {'content': data},
    if (error != null) 'error': error,
    if (conversationid != null) 'conversation_id': conversationid,
  };
  
  // 提取conversationid的便捷方法
  int? get channelId {
    // 优先使用本身的conversationid属性
    if (conversationid != null) {
      return conversationid;
    }
    
    // 其次从data中提取
    if (data is Map) {
      final mapData = data as Map;
      return mapData['conversation_id'] as int?;
    }
    return null;
  }
}

/// WebSocket消息管理器
///
/// 负责WebSocket消息的管理，包括：
/// - 消息序列化/反序列化
/// - 事件分发和订阅
/// - 通道管理
/// - 消息路由
class WsMessageManager extends GetxController {
  // 单例模式
  static final WsMessageManager _instance = WsMessageManager._internal();
  factory WsMessageManager() => _instance;

  // 连接管理器
  late final WsConnectionManager _connectionManager;

  // 通道管理
  int? _currentConversationId;

  // TODO: 将来迁移到GetX响应式变量，目前保持BehaviorSubject以确保兼容性
  final Map<int, List<BehaviorSubject<WsMsg>>> _channelEventHandlers = {};
  final Map<WsEvent, List<BehaviorSubject<WsMsg>>> _eventHandlers = {};

  // 消息跟踪器 - 仅跟踪待处理消息
  final MessageTracker _messageTracker = MessageTracker();

  // 订阅
  StreamSubscription? _messageSubscription;

  // 清理定时器
  Timer? _cleanupTimer;
  
  // 构造函数
  WsMessageManager._internal();

  @override
  void onInit() {
    super.onInit();
    try {
      // 确保WsConnectionManager已初始化 - 如果没有，先创建一个
      if (!Get.isRegistered<WsConnectionManager>()) {
        LogUtil.warn('WsConnectionManager未注册，尝试先初始化它');
        // 创建并自动注册WsConnectionManager
        WsConnectionManager();
      }

      // 获取WebSocket连接管理器实例 - 现在应该总是能找到它
      _connectionManager = Get.find<WsConnectionManager>();
      LogUtil.debug('WsMessageManager成功获取WsConnectionManager实例');

      _setupMessageListener();

      // 设置定期清理任务
      _setupCleanupTask();

      LogUtil.debug('WsMessageManager初始化完成');
    } catch (e) {
      LogUtil.error('WsMessageManager初始化失败: $e');
      throw Exception('WsMessageManager初始化失败: $e');
    }
  }

  @override
  void onClose() {
    // 停止监听消息
    _messageSubscription?.cancel();
    _cleanupTimer?.cancel();

    // 清理所有BehaviorSubject
    _eventHandlers.values.expand((subjects) => subjects).forEach((subject) {
      if (!subject.isClosed) {
        subject.close();
      }
    });
    _channelEventHandlers.values.expand((subjects) => subjects).forEach((subject) {
      if (!subject.isClosed) {
        subject.close();
      }
    });

    _eventHandlers.clear();
    _channelEventHandlers.clear();

    super.onClose();
    LogUtil.debug('WsMessageManager资源清理完成');
  }
  
  /// 设置定期清理任务
  void _setupCleanupTask() {
    // 取消现有定时器
    _cleanupTimer?.cancel();
    
    // 创建新定时器 - 每15秒清理一次，频率更高
    _cleanupTimer = Timer.periodic(const Duration(seconds: 15), (_) {
      _cleanupHandlers();
      _cleanupDuplicateSubscriptions();
    });
    
    LogUtil.debug('已设置处理器定期清理任务，间隔15秒');
  }
  
  /// 清理关闭的处理器
  void _cleanupHandlers() {
    try {
      int totalRemovedHandlers = 0;
      int totalRemovedChannels = 0;
      
      // 清理事件处理器
      final eventKeysToRemove = <WsEvent>[];
      _eventHandlers.forEach((event, handlers) {
        final initialCount = handlers.length;
        handlers.removeWhere((handler) => handler.isClosed);
        final removedCount = initialCount - handlers.length;
        totalRemovedHandlers += removedCount;
        
        if (handlers.isEmpty) {
          eventKeysToRemove.add(event);
          totalRemovedChannels++;
        }
      });
      
      // 移除空处理器列表
      for (final key in eventKeysToRemove) {
        _eventHandlers.remove(key);
      }
      
      // 清理通道处理器
      final channelKeysToRemove = <int>[];
      _channelEventHandlers.forEach((channelId, handlers) {
        final initialCount = handlers.length;
        handlers.removeWhere((handler) => handler.isClosed);
        final removedCount = initialCount - handlers.length;
        totalRemovedHandlers += removedCount;
        
        if (handlers.isEmpty) {
          channelKeysToRemove.add(channelId);
          totalRemovedChannels++;
        }
      });
      
      // 移除空处理器列表
      for (final key in channelKeysToRemove) {
        _channelEventHandlers.remove(key);
      }
      
      // 记录各通道处理器数量（用于调试）
      if (_channelEventHandlers.isNotEmpty) {
        LogUtil.debug('清理后各通道处理器数量:');
        _channelEventHandlers.forEach((channelId, handlers) {
          LogUtil.debug('  通道ID=$channelId 的处理器数量: ${handlers.length}');
        });
      }
      
      // 只在有清理操作时输出日志
      if (totalRemovedHandlers > 0 || totalRemovedChannels > 0) {
        LogUtil.debug('已清理${totalRemovedHandlers}个已关闭的处理器，移除了${totalRemovedChannels}个空通道');
      }
    } catch (e) {
      LogUtil.error('清理处理器失败: $e');
    }
  }
  
  /// 清理重复订阅
  void _cleanupDuplicateSubscriptions() {
    try {
      int totalDuplicatesRemoved = 0;
      
      // 对每个通道，检查重复订阅
      final channelIds = _channelEventHandlers.keys.toList();
      for (final channelId in channelIds) {
        if (_channelEventHandlers.containsKey(channelId)) {
          final handlers = _channelEventHandlers[channelId]!;
          
          // 如果处理器数量超过预期，尝试清理
          if (handlers.length > 2) { // 通常一个通道只需要2个处理器(chat_message和ai_reply)
            LogUtil.debug('通道ID=$channelId 的处理器数量(${handlers.length})超过预期，尝试清理重复订阅');
            
            // 记录已保留的处理器，根据事件类型去重
            final Set<String> keptHandlerEvents = {};
            final handlersToRemove = <BehaviorSubject<WsMsg>>[];
            
            // 保留每种事件类型的第一个处理器，移除重复的
            for (var handler in handlers) {
              if (handler.isClosed) {
                handlersToRemove.add(handler);
                continue;
              }
              
              // 尝试获取该处理器的最后一条消息，用于确定事件类型
              WsEvent? eventType;
              if (handler.hasValue) {
                eventType = handler.value.event;
              }
              
              final eventKey = eventType?.toString() ?? 'unknown';
              
              if (keptHandlerEvents.contains(eventKey)) {
                // 这是重复的事件类型，标记为移除
                handlersToRemove.add(handler);
                totalDuplicatesRemoved++;
              } else {
                // 首次出现的事件类型，保留
                keptHandlerEvents.add(eventKey);
              }
            }
            
            // 移除重复的处理器
            if (handlersToRemove.isNotEmpty) {
              for (var handler in handlersToRemove) {
                handlers.remove(handler);
                if (!handler.isClosed) {
                  handler.close();
                }
              }
              LogUtil.debug('已从通道ID=$channelId 移除${handlersToRemove.length}个重复处理器');
            }
          }
        }
      }
      
      if (totalDuplicatesRemoved > 0) {
        LogUtil.info('清理完成: 共移除$totalDuplicatesRemoved个重复订阅');
      }
    } catch (e) {
      LogUtil.error('清理重复订阅失败: $e');
    }
  }
  
  /// 设置消息监听器
  void _setupMessageListener() {
    // 取消现有订阅
    _messageSubscription?.cancel();
    
    // 订阅连接管理器的消息流
    _messageSubscription = _connectionManager.messageStream.listen((rawMessage) {
      if (rawMessage != null) {
        _processRawMessage(rawMessage);
      }
    });
  }
  
  /// 处理原始消息
  void _processRawMessage(String rawMessage) {
    // 添加日志：记录所有接收到的原始消息，不管格式是否正确
    LogUtil.info('WebSocket接收到消息: $rawMessage');
    
    try {
      // 解析JSON
      final Map<String, dynamic> jsonData = json.decode(rawMessage);
      
      // 创建WsMsg对象
      final message = WsMsg.fromJson(jsonData);
      
      // 立即分发消息，不缓存
      _dispatchMessage(message);
    } catch (e) {
      LogUtil.error('解析WebSocket消息失败: $e');
      LogUtil.debug('原始消息: $rawMessage');
    }
  }
  
  /// 分发消息到对应的处理器
  void _dispatchMessage(WsMsg message) {
    // 首先尝试获取消息中的role_id
    int? roleId;
    if (message.data is Map) {
      final mapData = message.data as Map;
      roleId = mapData['role_id'] is int ? mapData['role_id'] as int : 
              (mapData['role_id'] is String ? int.tryParse(mapData['role_id'].toString()) : null);
    }
    
    // 尝试获取消息中的message_id或reply_to_message_id
    String? messageId;
    if (message.data is Map) {
      final mapData = message.data as Map;
      messageId = mapData['message_id']?.toString() ?? mapData['reply_to_message_id']?.toString();
    }
    
    // 记录详细的分发信息
    if (message.event != WsEvent.ping && message.event != WsEvent.pong) {
      LogUtil.debug('分发消息: event=${message.event}, role_id=$roleId, conversation_id=${message.channelId}, message_id=$messageId');
      // 添加原始消息内容的日志
      if (message.data is Map) {
        LogUtil.debug('消息内容: ${json.encode(message.data)}');
      }
    }
    
    // 记录各个处理器的状态
    LogUtil.debug('当前活跃处理器情况:');
    if (roleId != null) {
      LogUtil.debug('  role_id=$roleId 的处理器数量: ${_channelEventHandlers[roleId]?.length ?? 0}');
    }
    if (message.channelId != null) {
      LogUtil.debug('  conversation_id=${message.channelId} 的处理器数量: ${_channelEventHandlers[message.channelId]?.length ?? 0}');
    }
    LogUtil.debug('  event=${message.event} 的全局处理器数量: ${_eventHandlers[message.event]?.length ?? 0}');
    
    // 优先基于role_id分发消息
    if (roleId != null && _channelEventHandlers.containsKey(roleId)) {
      final handlers = _channelEventHandlers[roleId]!;
      if (handlers.isNotEmpty) {
        LogUtil.debug('找到基于role_id=$roleId的处理器, 数量: ${handlers.length}');
        
        // 验证角色是否还有效（通过检查MessageTracker中的订阅）
        bool isRoleActive = false;
        try {
          final messageTracker = MessageTracker();
          final subscriptionCount = messageTracker.getRoleSubscriptionCount(roleId);
          isRoleActive = subscriptionCount > 0;
          
          if (!isRoleActive) {
            LogUtil.warn('角色$roleId在MessageTracker中已无订阅(订阅数=$subscriptionCount)，但仍收到消息，将清理通道处理器');
            // 清理该角色的处理器
            clearChannelHandlers(roleId);
            return; // 不再继续处理消息
          } else {
            LogUtil.debug('角色$roleId有效，订阅数=$subscriptionCount，继续处理消息');
          }
        } catch (e) {
          LogUtil.error('验证角色$roleId有效性失败: $e');
        }
        
        // 移除已关闭的处理器
        handlers.removeWhere((handler) => handler.isClosed);
        
        // 分发消息给所有活跃的处理器
        for (var handler in handlers) {
          if (!handler.isClosed) {
            handler.add(message);
            LogUtil.debug('消息已分发到role_id=$roleId的处理器');
          }
        }
      }
    }
    
    // 其次基于conversation_id分发消息（如果有）
    if (message.channelId != null && _channelEventHandlers.containsKey(message.channelId)) {
      final handlers = _channelEventHandlers[message.channelId]!;
      if (handlers.isNotEmpty) {
        LogUtil.debug('找到基于conversation_id=${message.channelId}的处理器, 数量: ${handlers.length}');
        
        // 移除已关闭的处理器
        handlers.removeWhere((handler) => handler.isClosed);
        
        // 分发消息给所有活跃的处理器
        for (var handler in handlers) {
          if (!handler.isClosed) {
            handler.add(message);
            LogUtil.debug('消息已分发到conversation_id=${message.channelId}的处理器');
          }
        }
      }
    }
    
    // 同时处理全局订阅（不管有没有conversationid都要处理）
    if (_eventHandlers.containsKey(message.event)) {
      LogUtil.debug('处理全局事件订阅: event=${message.event}');
      LogUtil.debug('找到 ${_eventHandlers[message.event]!.length} 个全局处理器');
      
      // 遍历处理器并输出状态
      for (var i = 0; i < _eventHandlers[message.event]!.length; i++) {
        var handler = _eventHandlers[message.event]![i];
        LogUtil.debug('全局处理器[$i]状态: closed=${handler.isClosed}');
        
        if (!handler.isClosed) {
          handler.add(message);
          LogUtil.debug('消息已分发到event=${message.event}的全局处理器[$i]');
        } else {
          LogUtil.debug('全局处理器[$i]已关闭，跳过分发');
        }
      }
    } else if (message.event != WsEvent.ping && message.event != WsEvent.pong) {
      LogUtil.debug('未找到event=${message.event}的全局处理器');
    }
    
    // 如果消息没有匹配到任何处理器，记录日志
    if ((roleId == null || !_channelEventHandlers.containsKey(roleId)) &&
        (message.channelId == null || !_channelEventHandlers.containsKey(message.channelId)) &&
        !_eventHandlers.containsKey(message.event)) {
      LogUtil.warn('消息没有找到匹配的处理器: event=${message.event}, role_id=$roleId, conversation_id=${message.channelId}');
    }
  }
  
  /// 订阅事件
  Stream<WsMsg> on(WsEvent event) {
    if (!_eventHandlers.containsKey(event)) {
      _eventHandlers[event] = [];
    }

    final subject = BehaviorSubject<WsMsg>();
    _eventHandlers[event]!.add(subject);
    return subject.stream;
  }

  /// 取消订阅
  void off(WsEvent event, BehaviorSubject<WsMsg> subject) {
    _eventHandlers[event]?.remove(subject);
    if (_eventHandlers[event]?.isEmpty ?? false) {
      _eventHandlers.remove(event);
    }

    if (!subject.isClosed) {
      subject.close();
    }
  }
  
  /// 切换到指定通道
  void switchChannel(int conversationId) {
    // 如果已经在此通道，则不需要操作
    if (_currentConversationId == conversationId) {
      LogUtil.debug('已经在通道 $conversationId，无需切换');
      return;
    }
    
    // 更新当前通道ID
    _currentConversationId = conversationId;
    LogUtil.info('已切换到通道: $conversationId');
  }
  
  /// 切换到全局通道
  /// 
  /// 将通道ID设置为null，表示不在任何具体通道中
  /// 通常在角色没有会话ID时使用
  void switchToGlobalChannel() {
    // 将当前通道ID设置为null
    _currentConversationId = null;
    LogUtil.info('已切换到全局通道，等待新会话ID分配');
  }
  
  /// 订阅特定通道的事件
  /// 
  /// [event] 事件类型
  /// [channelIdOrRoleId] 通道ID或角色ID
  /// 返回订阅流
  Stream<WsMsg> onChannel(WsEvent event, int channelIdOrRoleId) {
    // 检查是否已有相同事件类型的处理器，如果有则复用
    if (_channelEventHandlers.containsKey(channelIdOrRoleId)) {
      final handlers = _channelEventHandlers[channelIdOrRoleId]!;
      
      // 清理已关闭的处理器
      handlers.removeWhere((handler) => handler.isClosed);
      
      // 尝试查找相同事件类型的处理器
      for (var handler in handlers) {
        if (!handler.isClosed) {
          LogUtil.debug('复用角色/通道ID=$channelIdOrRoleId的现有处理器');
          return handler.stream.where((msg) => msg.event == event);
        }
      }
      
      LogUtil.debug('找不到可复用的处理器，将为角色/通道ID=$channelIdOrRoleId创建新处理器');
    }
    
    // 检查是否超过最大订阅数
    if (!_messageTracker.recordRoleSubscription(channelIdOrRoleId)) {
      LogUtil.warn('角色ID=$channelIdOrRoleId 的订阅数已达到最大值，使用现有订阅');
      // 尝试查找现有订阅
      if (_channelEventHandlers.containsKey(channelIdOrRoleId)) {
        for (var handler in _channelEventHandlers[channelIdOrRoleId]!) {
          if (!handler.isClosed) {
            return handler.stream.where((msg) => msg.event == event);
          }
        }
      }
      
      // 如果没有找到现有订阅，强制移除一个订阅以释放空间
      _messageTracker.forceRemoveOldestSubscription(channelIdOrRoleId);
      LogUtil.debug('已强制移除角色ID=${channelIdOrRoleId}的一个旧订阅');
    }
    
    if (!_channelEventHandlers.containsKey(channelIdOrRoleId)) {
      _channelEventHandlers[channelIdOrRoleId] = [];
    }
    
    final subject = BehaviorSubject<WsMsg>();
    _channelEventHandlers[channelIdOrRoleId]!.add(subject);
    
    LogUtil.debug('已为角色/通道ID=$channelIdOrRoleId创建新的处理器，当前处理器数量: ${_channelEventHandlers[channelIdOrRoleId]!.length}');

    // 过滤出对应事件的消息
    return subject.stream.where((msg) => msg.event == event);
  }
  
  /// 取消特定通道的订阅
  void offChannel(int channelIdOrRoleId, BehaviorSubject<WsMsg> subject) {
    if (_channelEventHandlers.containsKey(channelIdOrRoleId)) {
      _channelEventHandlers[channelIdOrRoleId]?.remove(subject);
      if (_channelEventHandlers[channelIdOrRoleId]?.isEmpty ?? false) {
        _channelEventHandlers.remove(channelIdOrRoleId);
      }
    }
    
    if (!subject.isClosed) {
      subject.close();
    }
  }
  
  /// 发送消息到当前通道
  /// 
  /// [event] 事件类型
  /// [data] 消息数据
  /// [channelId] 指定通道ID，如果为null则使用当前通道ID
  /// [messageId] 消息ID，用于追踪
  /// [roleId] 相关角色ID，用于追踪和路由
  bool sendToChannel({
    required WsEvent event,
    required dynamic data,
    int? channelId,
    String? messageId,
    int? roleId,
  }) {
    try {
      // 检查WebSocket是否已连接
      if (!_connectionManager.isConnected) {
        LogUtil.warn('WebSocket未连接，无法发送消息');
        throw WebSocketException('WebSocket未连接，无法发送消息', code: ErrorCodes.WS_MESSAGE_SEND_FAILED);
      }
      
      // 确定使用的通道ID
      final useChannelId = channelId ?? _currentConversationId;
      
      // 构建消息
      final message = WsMsg(
        event: event,
        data: data,
        conversationid: useChannelId,
      );
      
      // 跟踪消息
      if (messageId != null && roleId != null) {
        _messageTracker.addPendingMessage(messageId, roleId, conversationId: useChannelId);
      }
      
      // 发送消息
      final jsonStr = json.encode(message.toJson());
      _connectionManager.send(jsonStr);
      
      return true;
    } catch (e) {
      LogUtil.error('发送消息失败: $e');
      // 触发WebSocket错误事件
      GlobalEventState.to.triggerWebsocketError({
        'error': e,
        'errorType': 'SEND_FAILED',
        'errorCode': ErrorCodes.WS_MESSAGE_SEND_FAILED,
      });
      return false;
    }
  }
  
  /// 发送消息
  /// 
  /// [message] 要发送的消息
  /// 返回值：bool - 是否发送成功
  bool sendMessage(WsMsg message) {
    try {
      // 检查WebSocket是否已连接
      if (!_connectionManager.isConnected) {
        LogUtil.warn('WebSocket未连接，无法发送消息');
        throw WebSocketException('WebSocket未连接，无法发送消息', code: ErrorCodes.WS_MESSAGE_SEND_FAILED);
      }
      
      final jsonStr = json.encode(message.toJson());
      _connectionManager.send(jsonStr);
      return true;
    } catch (e) {
      LogUtil.error('发送消息失败: $e');
      // 触发WebSocket错误事件
      GlobalEventState.to.triggerWebsocketError({
        'error': e,
        'errorType': 'SEND_FAILED',
        'errorCode': ErrorCodes.WS_MESSAGE_SEND_FAILED,
      });
      return false;
    }
  }
  
  /// 获取当前通道ID
  int? get currentConversationId => _currentConversationId;
  
  /// 获取连接状态
  WsConnectionState get connectionState => _connectionManager.connectionState;
  
  /// 获取连接状态流
  Stream<WsConnectionState> get connectionStateStream => _connectionManager.connectionStateStream;
  
  /// 是否已连接
  bool get isConnected => _connectionManager.isConnected;
  
  /// 获取指定角色或通道的缓冲消息
  List<WsMsg> getBufferedMessages(int channelIdOrRoleId) {
    // 不存储缓冲消息，始终返回空列表
    return [];
  }
  
  /// 清除指定角色或通道的缓冲消息
  void clearBufferedMessages(int channelIdOrRoleId) {
    // 不执行任何操作，因为不存储缓冲消息
    LogUtil.debug('不存储缓冲消息，无需清理');
  }
  
  /// 清除所有缓冲消息
  void clearAllBufferedMessages() {
    // 不执行任何操作，因为不存储缓冲消息
    LogUtil.debug('不存储缓冲消息，无需清理');
  }
  
  /// 清除所有处理器和订阅
  void clearAllHandlersAndSubscriptions() {
    try {
      LogUtil.debug('开始清理WsMessageManager的所有处理器和订阅...');
      
      // 记录清理前的状态
      final channelIds = _channelEventHandlers.keys.toList();
      final eventTypes = _eventHandlers.keys.toList();
      int channelHandlersCount = 0;
      int eventHandlersCount = 0;
      
      // 详细记录要清理的通道
      if (channelIds.isNotEmpty) {
        LogUtil.debug('将清理以下通道ID的处理器: $channelIds');
        
        // 统计每个通道的处理器数量
        for (final channelId in channelIds) {
          final handlers = _channelEventHandlers[channelId] ?? [];
          LogUtil.debug('  通道ID=$channelId 的处理器数量: ${handlers.length}');
        }
      }
      
      // 关闭所有事件处理器
      for (var handlers in _eventHandlers.values) {
        for (var handler in handlers) {
          if (!handler.isClosed) {
            handler.close();
            eventHandlersCount++;
          }
        }
      }
      _eventHandlers.clear();
      
      // 关闭所有通道处理器
      for (var handlers in _channelEventHandlers.values) {
        for (var handler in handlers) {
          if (!handler.isClosed) {
            handler.close();
            channelHandlersCount++;
          }
        }
      }
      _channelEventHandlers.clear();
      
      // 同步清理MessageTracker，确保完全同步
      try {
        final messageTracker = MessageTracker();
        // 这里只清理内部跟踪状态，不清理外部订阅，避免循环调用
        messageTracker.clearAll();
        LogUtil.debug('已同步清理MessageTracker状态');
      } catch (e) {
        LogUtil.error('清理MessageTracker失败: $e');
      }
      
      LogUtil.info('已清理WsMessageManager: 关闭了${channelHandlersCount}个通道处理器(原通道: $channelIds)和${eventHandlersCount}个事件处理器(原事件: $eventTypes)');
    } catch (e) {
      LogUtil.error('清理WsMessageManager的所有处理器和订阅失败: $e');
    }
  }
  
  /// 销毁
  void dispose() {
    LogUtil.debug('开始销毁WsMessageManager...');
    
    // 取消清理定时器
    if (_cleanupTimer != null) {
      LogUtil.debug('取消清理定时器');
      _cleanupTimer?.cancel();
      _cleanupTimer = null;
    }
    
    // 取消消息订阅
    if (_messageSubscription != null) {
      LogUtil.debug('取消WebSocket消息订阅');
      _messageSubscription?.cancel();
      _messageSubscription = null;
    }
    
    // 关闭所有事件处理器
    int eventHandlersCount = 0;
    for (var handlers in _eventHandlers.values) {
      for (var handler in handlers) {
        if (!handler.isClosed) {
          handler.close();
          eventHandlersCount++;
        }
      }
    }
    _eventHandlers.clear();
    LogUtil.debug('已关闭$eventHandlersCount个事件处理器');
    
    // 关闭所有通道处理器
    int channelHandlersCount = 0;
    for (var handlers in _channelEventHandlers.values) {
      for (var handler in handlers) {
        if (!handler.isClosed) {
          handler.close();
          channelHandlersCount++;
        }
      }
    }
    _channelEventHandlers.clear();
    LogUtil.debug('已关闭$channelHandlersCount个通道处理器');
    
    LogUtil.debug('WsMessageManager已销毁');
  }

  /// 清除指定通道的所有处理器和订阅
  /// 
  /// [channelId] 通道ID（通常是角色ID或会话ID）
  /// [isRoleId] 是否是角色ID，如果是则会检查MessageTracker中的订阅状态
  /// 返回是否成功清除
  bool clearChannelHandlers(int channelId, {bool isRoleId = true}) {
    try {
      // 检查该通道是否有处理器
      if (!_channelEventHandlers.containsKey(channelId)) {
        LogUtil.debug('通道ID=$channelId 没有处理器需要清理');
        return false;
      }
      
      // 如果是角色ID，检查MessageTracker中的订阅状态
      if (isRoleId) {
        try {
          final messageTracker = MessageTracker();
          final subscriptionCount = messageTracker.getRoleSubscriptionCount(channelId);
          LogUtil.debug('角色ID=$channelId 在MessageTracker中的订阅数: $subscriptionCount');
          
          // 如果MessageTracker中还有订阅，记录日志，这可能表示不同步
          if (subscriptionCount > 0) {
            LogUtil.warn('警告: 角色ID=$channelId 在MessageTracker中仍有$subscriptionCount个订阅，但正在清理其通道处理器');
          }
        } catch (e) {
          LogUtil.error('检查角色ID=$channelId 的MessageTracker订阅失败: $e');
        }
      }
      
      // 关闭指定通道的所有处理器
      final handlers = _channelEventHandlers[channelId]!;
      int closedCount = 0;
      
      for (var handler in handlers) {
        if (!handler.isClosed) {
          handler.close();
          closedCount++;
        }
      }
      
      // 从映射中移除该通道
      _channelEventHandlers.remove(channelId);
      
      // 详细记录清理结果
      LogUtil.debug('已清理通道ID=$channelId 的$closedCount个处理器');
      
      return true;
    } catch (e) {
      LogUtil.error('清理通道ID=$channelId 的处理器失败: $e');
      return false;
    }
  }
}