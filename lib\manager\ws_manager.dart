import 'dart:async';
import 'package:flutter/material.dart';
import 'package:rolio/common/constants/ws_constants.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/toast_util.dart';
import 'package:rolio/manager/ws_connection_manager.dart';
import 'package:rolio/manager/ws_message_manager.dart';
import 'package:get/get.dart';
import 'package:rolio/manager/global_state.dart';
import 'package:rolio/common/state/global_event_state.dart';
import 'package:rolio/modules/login/service/login_service.dart';
import 'package:rolio/env.dart';
import 'package:rolio/common/utils/message_tracker.dart';
import 'dart:math' as math;

/// WebSocket管理器
/// 
/// 组合服务，整合WsConnectionManager和WsMessageManager的功能，
/// 确保向后兼容，同时提供更清晰的职责划分
class WsManager with WidgetsBindingObserver {
  // 单例模式
  static final WsManager _instance = WsManager._internal();
  factory WsManager() => _instance;
  
  // 连接管理器和消息管理器
  late final WsConnectionManager _connectionManager;
  late final WsMessageManager _messageManager;
  
  // 全局状态
  final GlobalState _globalState = Get.find<GlobalState>();
  
  // 事件订阅
  StreamSubscription? _userIdentityChangedSubscription;
  
  // 防止重复连接
  bool _isReconnecting = false;
  
  // 暂停WebSocket操作标志
  bool _pauseWebSocketOperations = false;
  
  // 构造函数
  WsManager._internal() {
    try {
      // 检查依赖是否已注册
      if (!Get.isRegistered<WsConnectionManager>()) {
        LogUtil.error('WsConnectionManager未注册，这是一个严重问题！');
        throw Exception('初始化WsManager失败: WsConnectionManager未注册');
      }
      
      if (!Get.isRegistered<WsMessageManager>()) {
        LogUtil.error('WsMessageManager未注册，这是一个严重问题！');
        throw Exception('初始化WsManager失败: WsMessageManager未注册');
      }
      
      // 获取已注册的实例
      _connectionManager = Get.find<WsConnectionManager>();
      LogUtil.debug('WsManager成功获取WsConnectionManager实例');
      
      _messageManager = Get.find<WsMessageManager>();
      LogUtil.debug('WsManager成功获取WsMessageManager实例');
      
      _setupEventListeners();
      
      // 添加WebSocket连接状态监听
      _setupConnectionStateListener();
      
      LogUtil.debug('WsManager初始化完成');
    } catch (e) {
      LogUtil.error('WsManager初始化失败: $e');
      rethrow;
    }
  }
  
  // 待处理消息的计数器
  final RxInt _pendingMessagesCount = 0.obs;
  
  /// 设置事件监听器
  void _setupEventListeners() {
    try {
          
      // 监听认证开始事件
      ever(GlobalEventState.to.authProcessing, (bool processing) {
        if (processing) {
          _pauseWebSocketOperations = true;
          LogUtil.debug('收到认证开始事件，暂停WebSocket操作');
        }
      });
      
      // 监听认证完成事件 - 作为WebSocket重连的唯一触发点
      ever(GlobalEventState.to.authProcessing, (bool processing) {
        if (!processing) {
          _pauseWebSocketOperations = false;
          LogUtil.debug('收到认证完成事件，恢复WebSocket操作');

          // 获取当前用户ID进行重连
          final userId = _globalState.currentUser.value?.uid;
          if (userId != null && userId.isNotEmpty) {
            LogUtil.info('认证流程完成，开始WebSocket重连(唯一触发点), userId: $userId');
            _reconnectWithUser(userId);
          } else {
            LogUtil.warn('认证流程完成但userId为空，跳过WebSocket重连');
          }
        }
      });
          
      LogUtil.debug('已设置认证流程事件监听（统一重连入口）');
    } catch (e) {
      LogUtil.error('设置事件监听器失败: $e');
    }
  }
  
  /// 处理用户身份变更事件
  void _handleUserIdentityChanged(Map<String, dynamic> event) {
    try {
      final data = event['data'];
      if (data is Map && data.containsKey('userId')) {
        final userId = data['userId'] as String?;
        
        LogUtil.info('收到用户身份变更事件，用户ID: $userId');
        
        // 如果用户ID为空，不进行处理
        if (userId == null || userId.isEmpty) {
          LogUtil.warn('用户身份变更事件中的用户ID为空，跳过WebSocket重连');
          return;
        }
        
        // 如果当前暂停了WebSocket操作，则记录但不执行
        if (_pauseWebSocketOperations) {
          LogUtil.info('WebSocket操作已暂停，暂不处理用户身份变更事件，等待认证流程完成');
          return;
        }
        
        // 重新连接WebSocket
        _reconnectWithUser(userId);
      }
    } catch (e) {
      LogUtil.error('处理用户身份变更事件失败: $e');
    }
  }
  
  /// 使用指定用户重新连接WebSocket
  Future<void> _reconnectWithUser(String userId) async {
    // 防止重复重连
    if (_isReconnecting) {
      LogUtil.info('已有重连操作进行中，跳过此次重连');
      return;
    }
    
    // 如果WebSocket操作已暂停，则不执行重连
    if (_pauseWebSocketOperations) {
      LogUtil.info('WebSocket操作已暂停，暂不执行重连，等待认证流程完成');
      return;
    }
    
    _isReconnecting = true;
    
    try {
      // 先断开现有连接
      if (_connectionManager.isConnected) {
        LogUtil.info('断开现有WebSocket连接以准备重连');
        await disconnect();
        
        // 短暂延迟确保连接完全关闭
        await Future.delayed(const Duration(milliseconds: 300));
      }
      
      // 获取当前用户的token并确保有效
      var token = _globalState.accessToken.value;
      
      // 如果token无效，尝试获取新token
      if (token == null || token.isEmpty) {
        LogUtil.warn('Token为空或无效，尝试获取新token');
        
        if (Get.isRegistered<LoginService>()) {
          final loginService = Get.find<LoginService>();
          final newToken = await loginService.getIdToken(forceRefresh: true);
          if (newToken != null && newToken.isNotEmpty) {
            await _globalState.setAccessToken(newToken);
            token = newToken;
            LogUtil.debug('已获取新token');
          } else {
            LogUtil.error('无法获取有效token，跳过WebSocket连接');
            return;
          }
        } else {
          LogUtil.error('LoginService未注册，无法获取token，跳过WebSocket连接');
          return;
        }
      }
      
      // 建立新连接
      LogUtil.info('使用新用户ID重新连接WebSocket: $userId，token前10位: ${token.length > 10 ? token.substring(0, 10) + '...' : 'invalid'}');
      
      // 构建WebSocket URL
      final wsUrl = _buildWebSocketUrl(token);
      
      // 直接使用连接管理器连接，而不是通过消息管理器
      final result = await _connectionManager.connect(wsUrl, userId: userId);
      
      if (result) {
        LogUtil.info('用户身份变更后WebSocket重连成功');
      } else {
        LogUtil.error('用户身份变更后WebSocket重连失败，将在后台继续尝试');
        // 后台继续尝试重连，但使用延迟避免立即重连
        Future.delayed(const Duration(seconds: 5), () => reconnect());
      }
    } catch (e) {
      LogUtil.error('用户身份变更后WebSocket重连失败: $e');
    } finally {
      _isReconnecting = false;
    }
  }
  
  /// 设置连接状态监听
  void _setupConnectionStateListener() {
    _connectionManager.connectionStateStream.listen((state) {
      LogUtil.debug('WebSocket连接状态变更: $state');
      
      // 当连接断开或失败时，清空待发送消息和重置AI回复状态
      if (state == WsConnectionState.disconnected || 
          state == WsConnectionState.failed) {
        _handleConnectionLost();
      }
    });
  }
  
  /// 处理连接断开的情况
  void _handleConnectionLost() {
    try {
      // 生成唯一的事件ID，避免重复处理
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final eventId = 'disconnect_${timestamp}_${math.Random().nextInt(1000)}';
      
      LogUtil.info('WebSocket连接已断开，清理相关状态... (事件ID:$eventId)');
      
      // 1. 获取待处理消息数量前先确保MessageTracker可用
      _pendingMessagesCount.value = _getPendingMessagesCount();
      
      // 2. 不再清空待发送消息队列，以保留角色订阅
      // _clearPendingMessages(); // 注释掉，不再调用
      LogUtil.debug('WebSocket断开连接，保留角色订阅以便重连后恢复');
      
      // 3. 重置AI回复状态 - 断开连接时触发全局重置
      _resetAiReplyingStates(eventId); // 不传递roleId，触发全局重置
      
      // 4. 如果有待处理消息，显示提示
      if (_pendingMessagesCount.value > 0) {
        _showConnectionLostNotification();
      }
      
      // 5. 发出连接断开事件，让其他组件可以响应
      GlobalEventState.to.triggerWebsocketDisconnected();
      
      LogUtil.debug('连接断开处理完成，但保留所有角色订阅 (事件ID:$eventId)');
    } catch (e) {
      LogUtil.error('处理连接断开时发生错误: $e');
    }
  }
  
  /// 获取待处理消息数量
  int _getPendingMessagesCount() {
    try {
      // 从MessageTracker获取待处理消息数量
      if (Get.isRegistered<MessageTracker>()) {
        final tracker = Get.find<MessageTracker>();
        
        // 先检查MessageTracker中的待处理消息
        final pendingCount = tracker.getPendingMessageCount();
        
        // 如果MessageTracker返回0，尝试检查是否有其他数据源可以提供待处理消息信息
        if (pendingCount == 0) {
          // 记录日志，以便调试
          LogUtil.debug('MessageTracker中没有待处理消息，检查是否有其他待处理消息');
          
          // 可以在这里添加其他检查方式，如果有的话
          
          // 检查完毕，仍然返回0
          return 0;
        }
        
        // 返回MessageTracker中的待处理消息数量
        LogUtil.debug('检测到${pendingCount}条待处理消息');
        return pendingCount;
      }
      
      LogUtil.warn('MessageTracker未注册，无法获取待处理消息数量');
      return 0;
    } catch (e) {
      LogUtil.error('获取待处理消息数量失败: $e');
      return 0;
    }
  }
  
  /// 清空待发送消息队列
  void _clearPendingMessages() {
    try {
      // 清空消息跟踪器中的待处理消息
      if (Get.isRegistered<MessageTracker>()) {
        final tracker = Get.find<MessageTracker>();
        
        // 记录清空前的消息数量，便于调试
        final countBefore = tracker.getPendingMessageCount();
        
        // 记录现有角色订阅信息，以便维持
        final subscribedRoles = tracker.getAllSubscribedRoleIds();
        LogUtil.debug('保留现有角色订阅: $subscribedRoles');
        
        // 不要使用clearAll，因为它会清除所有角色订阅
        // 而是只清除待处理消息，保留角色订阅信息
        // 通过循环，对每个消息单独处理，确保不影响订阅状态
        final pendingMessages = tracker.getAllPendingMessageIds();
        for (final messageId in pendingMessages) {
          tracker.removeMessageFromPending(messageId);
        }
        
        LogUtil.debug('已清空所有待处理消息（清空前数量：$countBefore），但保留所有角色订阅');
      } else {
        LogUtil.warn('MessageTracker未注册，跳过清空待处理消息');
      }
    } catch (e) {
      LogUtil.error('清空待处理消息失败: $e');
    }
  }
  
  /// 重置AI回复状态
  void _resetAiReplyingStates(String eventId, {int? roleId}) {
    try {
      // 使用响应式状态触发AI回复状态重置事件
      // 所有监听此事件的服务都会重置其AI回复状态
      GlobalEventState.to.triggerAiReplyReset(eventId: eventId, roleId: roleId);
      
      LogUtil.debug('已触发AI回复状态重置事件(ID:$eventId, roleId:$roleId)');
    } catch (e) {
      LogUtil.error('触发AI回复状态重置事件失败: $e');
    }
  }
  
  /// 显示连接断开提示
  void _showConnectionLostNotification() {
    try {
      // 显示连接断开提示
      ToastUtil.error('Message sending failed due to connection issues. Please try again.');
      
      LogUtil.info('已显示连接断开提示');
    } catch (e) {
      LogUtil.error('显示连接断开提示失败: $e');
    }
  }
  
  /// 构建WebSocket URL
  String _buildWebSocketUrl(String token) {
    final protocol = Env.envConfig.wsProtocol ?? 'ws';
    
    // 处理serverAddr，确保格式正确
    String serverAddr = Env.envConfig.serverAddr;
    // 移除末尾斜杠
    if (serverAddr.endsWith('/')) {
      serverAddr = serverAddr.substring(0, serverAddr.length - 1);
    }
    
    // 构建WebSocket连接URL，使用ws://{host}/ws?token={token}格式
    return '$protocol://$serverAddr/ws?token=$token';
  }
  
  // === 连接管理相关方法（委托给WsConnectionManager） ===
  
  /// 获取连接状态
  WsConnectionState get connectionState => _connectionManager.connectionState;
  
  /// 连接状态流
  Stream<WsConnectionState> get connectionStateStream => _connectionManager.connectionStateStream;
  
  /// 重连状态流
  Stream<ReconnectStatus?> get reconnectStream => _connectionManager.reconnectStream;
  
  /// 是否已连接
  bool get isConnected => _connectionManager.isConnected;
  
  /// 连接到WebSocket服务器
  /// 
  /// [url] WebSocket服务器URL
  /// [userId] 用户ID，用于身份验证
  /// 返回是否成功连接
  Future<bool> connect(String url, {String? userId}) {
    return _connectionManager.connect(url, userId: userId);
  }
  
  /// 连接到全局WebSocket服务器
  /// 
  /// [userId] 用户ID，用于身份验证
  /// 返回是否成功连接
  Future<bool> connectGlobal({required String userId}) async {
    // 防止重复连接
    if (_isReconnecting) {
      LogUtil.info('已有连接操作进行中，跳过此次连接');
      return Future.value(false);
    }
    
    _isReconnecting = true;
    
    try {
      // 使用更可靠的方式获取token
      String? token;
      
      // 无论_wsUrl是否为空，都检查Token并在必要时强制刷新
      if (Get.isRegistered<LoginService>()) {
        final loginService = Get.find<LoginService>();
        
        // 首先获取当前Token状态（不强制刷新）
        token = _globalState.accessToken.value;
        
        // 检查当前Token是否为空或者即将过期（此处直接强制刷新，因为Firebase没有提供检查有效期的简单方法）
        LogUtil.info('检查Token有效性，确保Token不会过期...');
        if (token == null || token.isEmpty) {
          LogUtil.warn('当前Token为空，需要获取新Token');
          token = await loginService.getIdToken(forceRefresh: true);
        } else {
          // 强制刷新Token，确保不会即将过期
          LogUtil.info('无论当前Token是否为空，都进行强制刷新，确保不会过期');
          token = await loginService.getIdToken(forceRefresh: true);
        }
        
        // 如果无法获取token，记录日志并重试一次
        if (token == null || token.isEmpty) {
          LogUtil.warn('首次获取token失败，等待500ms后重试');
          await Future.delayed(const Duration(milliseconds: 500));
          token = await loginService.getIdToken(forceRefresh: true);
        }
      } 
      
      // 如果仍然无法获取token，尝试再次尝试
      if (token == null || token.isEmpty) {
        LogUtil.warn('通过LoginService获取token失败，最后尝试再次强制刷新');
        
        if (Get.isRegistered<LoginService>()) {
          final loginService = Get.find<LoginService>();
          token = await loginService.getIdToken(forceRefresh: true);
          
          // 如果获取到新token，更新全局状态
          if (token != null && token.isNotEmpty) {
            await _globalState.setAccessToken(token);
            LogUtil.debug('已更新全局状态的Token');
          }
        } else {
          // 最后尝试从全局状态获取
          token = _globalState.accessToken.value;
        }
      }
      
      // 检查token是否有效
      if (token == null || token.isEmpty) {
        LogUtil.error('连接WebSocket失败：token无效或为空，已尝试多次刷新');
        _isReconnecting = false;
        return Future.value(false);
      }
      
      // 记录token信息（只记录前几个字符，确保安全）
      final tokenPrefix = token.length > 10 ? token.substring(0, 10) + '...' : '无效';
      LogUtil.debug('连接全局WebSocket，userId: $userId，token前缀: $tokenPrefix，token长度: ${token.length}');
      
      // 构建WebSocket URL（使用最新刷新的token，而不使用之前可能缓存的URL）
      final wsUrl = _buildWebSocketUrl(token);
      LogUtil.info('连接到全局WebSocket服务器: $wsUrl');
      
      // 直接使用连接管理器连接，而不是通过消息管理器
      final result = await _connectionManager.connect(wsUrl, userId: userId);
      
      // 如果连接失败，记录详细信息并安排自动重连
      if (!result) {
        LogUtil.warn('WebSocket连接失败，将再次尝试刷新Token后重试');
        
        // 连接失败时，再次强制刷新Token
        if (Get.isRegistered<LoginService>()) {
          final loginService = Get.find<LoginService>();
          token = await loginService.getIdToken(forceRefresh: true);
          
          if (token != null && token.isNotEmpty) {
            // 更新全局状态
            await _globalState.setAccessToken(token);
            
            // 使用新token构建URL
            final newWsUrl = _buildWebSocketUrl(token);
            LogUtil.info('使用刷新后的Token重试连接: $newWsUrl');
            
            // 延迟1秒后重试
            await Future.delayed(const Duration(seconds: 1));
            final retryResult = await _connectionManager.connect(newWsUrl, userId: userId);
            
            if (retryResult) {
              LogUtil.info('使用刷新后的Token重连成功');
              _isReconnecting = false;
              return true;
            } else {
              LogUtil.error('使用刷新后的Token重连失败');
            }
          } else {
            LogUtil.error('刷新Token失败，无法重新连接WebSocket');
          }
        }
        
        // 如果仍然失败，进行常规重试
        await Future.delayed(const Duration(seconds: 2));
        final retryResult = await _connectionManager.connect(wsUrl, userId: userId);
        
        if (retryResult) {
          LogUtil.info('WebSocket自动重试连接成功');
          _isReconnecting = false;
          return true;
        } else {
          LogUtil.error('WebSocket自动重试连接失败');
        }
      }
      
      return result;
    } catch (e) {
      LogUtil.error('连接全局WebSocket失败: $e');
      return Future.value(false);
    } finally {
      _isReconnecting = false;
    }
  }
  
  /// 断开WebSocket连接
  Future<void> disconnect() async {
    return await _connectionManager.disconnect();
  }
  
  /// 重新连接WebSocket
  /// 
  /// 尝试重新建立WebSocket连接
  /// 返回是否成功重新连接
  Future<bool> reconnect() async {
    // 防止重复重连
    if (_isReconnecting) {
      LogUtil.info('已有重连操作进行中，跳过此次重连');
      return Future.value(false);
    }
    
    _isReconnecting = true;
    LogUtil.info('尝试重新连接WebSocket');
    
    try {
      // 委托给连接管理器处理重连
      return await _connectionManager.reconnect();
    } finally {
      _isReconnecting = false;
    }
  }
  
  /// 手动重连WebSocket
  /// 
  /// 重置重连计数并尝试重新连接，用于用户手动触发重连
  Future<bool> manualReconnect() async {
    // 防止重复重连
    if (_isReconnecting) {
      LogUtil.info('已有重连操作进行中，跳过此次手动重连');
      return Future.value(false);
    }
    
    _isReconnecting = true;
    LogUtil.info('用户手动触发WebSocket重连');
    
    try {
      // 委托给连接管理器处理手动重连
      return await _connectionManager.manualReconnect();
    } finally {
      _isReconnecting = false;
    }
  }
  
  /// 发送消息到当前通道
  bool sendToChannel({
    required WsEvent event, 
    required dynamic data, 
    int? channelId,
    String? messageId,
    int? roleId,
  }) {
    return _messageManager.sendToChannel(
      event: event,
      data: data,
      channelId: channelId,
      messageId: messageId,
      roleId: roleId,
    );
  }
  
  /// 发送消息
  bool sendMessage(WsMsg message) {
    return _messageManager.sendMessage(message);
  }
  
  /// 订阅事件
  Stream<WsMsg> on(WsEvent event) {
    return _messageManager.on(event);
  }
  
  /// 取消订阅
  void off(WsEvent event, dynamic subject) {
    _messageManager.off(event, subject);
  }
  
  /// 切换到指定通道
  void switchChannel(int conversationId) {
    _messageManager.switchChannel(conversationId);
  }
  
  /// 切换到全局通道
  void switchToGlobalChannel() {
    _messageManager.switchToGlobalChannel();
  }
  
  /// 订阅特定通道的事件
  Stream<WsMsg> onChannel(WsEvent event, int channelIdOrRoleId) {
    return _messageManager.onChannel(event, channelIdOrRoleId);
  }
  
  /// 获取当前通道ID
  int? get currentconversationid => _messageManager.currentConversationId;
  
  /// 销毁
  void dispose() {
    _userIdentityChangedSubscription?.cancel();
    _connectionManager.dispose();
    _messageManager.dispose();
    
    LogUtil.debug('WsManager已销毁');
  }
}