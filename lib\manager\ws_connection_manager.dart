import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:rolio/common/constants/ws_constants.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/utils/message_tracker.dart';
import 'package:rolio/common/state/global_event_state.dart';
import 'package:get/get.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:synchronized/synchronized.dart';

/// WebSocket连接管理器
///
/// 负责WebSocket连接的基础管理，包括：
/// - 建立和断开连接
/// - 连接状态管理
/// - 心跳维护
/// - 重连逻辑
/// - 应用生命周期监听
class WsConnectionManager extends GetxController with WidgetsBindingObserver {
  // 单例模式
  static final WsConnectionManager _instance = WsConnectionManager._internal();
  factory WsConnectionManager() => _instance;
  // 构造函数
  WsConnectionManager._internal() {
    // 确保不依赖其他尚未初始化的组件
    LogUtil.debug('WsConnectionManager初始化');
    
    // 自动注册到GetX容器，确保总是先初始化
    if (!Get.isRegistered<WsConnectionManager>()) {
      Get.put<WsConnectionManager>(this, permanent: true);
      LogUtil.debug('WsConnectionManager自动注册到GetX容器');
    }
    
    // 注册应用生命周期监听
    WidgetsBinding.instance.addObserver(this);
    
    // 设置网络状态监听器
    _setupNetworkListener();
  }
  
  // 网络连接状态监听
  StreamSubscription<List<ConnectivityResult>>? _networkSubscription;
  bool _isNetworkAvailable = true; // 默认认为网络可用
  
  // WebSocket通道
  WebSocketChannel? _channel;
  
  // 当前连接信息
  String? _wsUrl;
  String? _userId;
  
  // 心跳相关
  Timer? _heartbeatTimer;
  Timer? _heartbeatTimeoutTimer;
  int _missedHeartbeats = 0;  // 跟踪连续丢失的心跳数
  final _maxMissedHeartbeats = 4;  // 允许连续丢失的最大心跳数，从3增加到4以提高容错性
  DateTime? _lastPongTime;  // 最后一次收到pong的时间
  
  // 重连相关
  Timer? _reconnectTimer;
  int _reconnectAttempts = 0;
  final _reconnectMaxDelayMs = 30000; // 最大重连延迟(毫秒)
  final _maxReconnectAttempts = 10; // 最大重连尝试次数

  // 使用GetX响应式变量替代BehaviorSubject
  final Rx<ReconnectStatus?> _reconnectStatus = Rx<ReconnectStatus?>(null);
  final Rx<WsConnectionState> _connectionState = WsConnectionState.disconnected.obs;
  final Rx<String?> _messageStream = Rx<String?>(null);

  // 连接状态
  WsConnectionState get connectionState => _connectionState.value;
  Stream<WsConnectionState> get connectionStateStream => _connectionState.stream;
  bool get isConnected => connectionState == WsConnectionState.connected;

  // 重连状态
  Stream<ReconnectStatus?> get reconnectStream => _reconnectStatus.stream;

  // 消息流
  Stream<String?> get messageStream => _messageStream.stream;
  
  // 应用状态跟踪
  AppLifecycleState _appState = AppLifecycleState.resumed;
  
  // 连接配置
  WsConnectionConfig _config = WsConnectionConfig.defaultConfig;
  
  // 使用synchronized包的Lock实现锁
  final _connectionLock = Lock();
  final _stateLock = Lock();
  final _heartbeatLock = Lock();
  final _reconnectLock = Lock();
  
  // 连接状态跟踪
  bool _isConnecting = false;
  
  /// 设置连接配置
  void setConfig(WsConnectionConfig config) {
    _config = config;
    LogUtil.debug('已更新WebSocket连接配置: ${config.toString()}');
  }
  
  /// 连接到WebSocket服务器
  /// 
  /// [url] WebSocket服务器URL，格式为ws://{host}/ws?token={token}
  /// [userId] 用户ID，用于内部记录
  /// 返回是否成功连接
  Future<bool> connect(String url, {String? userId}) async {
    // 使用Lock对象的synchronized方法防止多个连接请求并发执行
    return _connectionLock.synchronized(() async {
      // 如果已经有连接操作在进行中
      if (_isConnecting) {
        LogUtil.warn('已有连接正在进行中，忽略此次连接请求');
        return false;
      }
      
      _isConnecting = true;
      
      try {
        // 记录连接参数
        LogUtil.info('开始连接WebSocket: $url，用户ID: $userId');
        
        // 如果已连接到相同URL和用户ID，不需要重新连接
        if (_channel != null && _wsUrl == url && _userId == userId && isConnected) {
          LogUtil.info('已连接到相同的WebSocket URL和用户ID，无需重新连接');
          _isConnecting = false;
          return true;
        }
        
        // 如果已连接，先断开
        if (_channel != null) {
          LogUtil.info('断开已有的WebSocket连接');
          await disconnect();
        }
        
        // 保存连接信息
        _wsUrl = url;
        _userId = userId;
        
        // 更新连接状态
        _updateState(WsConnectionState.connecting);
        
        // 建立WebSocket连接，Token已在URL中
        _channel = WebSocketChannel.connect(Uri.parse(url));
        
        // 监听连接状态
        bool connected = false;
        try {
          await _channel!.ready;
          LogUtil.info('WebSocket连接成功: $url，用户ID: $userId');
          _updateState(WsConnectionState.connected);
          
          // 开始心跳
          _startHeartbeat();
          
          // 重置重连尝试次数
          _reconnectAttempts = 0;
          connected = true;
        } catch (error) {
          LogUtil.error('WebSocket连接失败: $error，URL: $url，用户ID: $userId');
          _handleDisconnect(error);
          connected = false;
        }
        
        if (connected) {
          // 监听消息
          _channel!.stream.listen(
            (data) {
              // 将接收到的消息发送到消息流
              final messageStr = data.toString();
              _messageStream.value = messageStr;

              // 处理心跳响应
              try {
                // 尝试解析JSON
                final jsonData = json.decode(messageStr);
                // 检查是否为pong消息
                if (jsonData is Map && jsonData['event'] == 'pong') {
                  LogUtil.debug('收到pong消息: ${jsonData['data']}');
                  _handlePong();
                }
              } catch (e) {
                // 如果解析失败，尝试使用字符串匹配
                if (messageStr.contains('"event":"pong"')) {
                  LogUtil.debug('通过字符串匹配检测到pong消息');
                  _handlePong();
                }
              }
            },
            onError: _handleConnectionError,
            onDone: _handleConnectionClosed,
            cancelOnError: false,
          );
        }
        
        return connected;
      } catch (e) {
        LogUtil.error('建立WebSocket连接失败: $e，URL: $url，用户ID: $userId');
        // 确保用异常结束的连接更新为失败状态
        _updateState(WsConnectionState.failed);
        return false;
      } finally {
        _isConnecting = false;
      }
    });
  }

  /// 断开WebSocket连接
  Future<void> disconnect() async {
    try {
      // 取消心跳
      _cancelHeartbeat();
      
      // 取消重连
      _cancelReconnect();
      
      // 关闭通道
      if (_channel != null) {
        await _channel!.sink.close(1000, '正常关闭');
        _channel = null;
      }
      
      // 更新状态
      _updateState(WsConnectionState.disconnected);
      
      // WebSocket断开时不清空消息跟踪器，保留角色订阅
      LogUtil.debug('已断开WebSocket连接，但保留角色订阅状态以便重连时恢复');
      
      LogUtil.info('已断开WebSocket连接');
    } catch (e) {
      LogUtil.error('断开WebSocket连接失败: $e');
    }
  }
  
  /// 清空消息跟踪器 - 只在特殊情况下调用，如用户登出、强制清理等
  void _cleanupMessageTracker() {
    try {
      LogUtil.debug('准备清理消息跟踪器...');
      
      // 记录当前状态
      final messageTracker = MessageTracker();
      final pendingCount = messageTracker.getPendingMessageCount();
      final subscribedRoles = messageTracker.getAllSubscribedRoleIds();
      
      // 仅在用户主动登出、切换账号或需要强制清理时才调用
      LogUtil.warn('⚠️ 完全清空MessageTracker - 这会清除所有角色订阅和待处理消息! 当前角色订阅: $subscribedRoles, 待处理消息: $pendingCount');
      LogUtil.warn('⚠️ 此操作仅应在用户登出、切换账号或强制重置时执行，临时断线重连不应清空订阅');
      
      // MessageTracker().clearAll();  // 不再自动清空，改为手动调用
    } catch (e) {
      LogUtil.error('清空消息跟踪器失败: $e');
    }
  }
  
  /// 发送消息
  /// 
  /// [message] 要发送的消息
  void send(String message) {
    try {
      if (_channel == null || !isConnected) {
        LogUtil.warn('WebSocket未连接，无法发送消息');
        return;
      }
      
      _channel!.sink.add(message);
      
      // 只为非心跳消息输出日志
      if (!message.contains('"event":"ping"') && !message.contains('"event":"pong"')) {
        LogUtil.debug('已发送WebSocket消息: $message');
      }
    } catch (e) {
      LogUtil.error('发送WebSocket消息失败: $e');
    }
  }
  
  /// 更新连接状态
  void _updateState(WsConnectionState newState) {
    _stateLock.synchronized(() {
      if (_connectionState.value != newState) {
        _connectionState.value = newState;
        LogUtil.debug('WebSocket连接状态更新: $newState');
      }
    });
  }
  
  /// 开始心跳
  void _startHeartbeat() {
    // 取消现有心跳
    _cancelHeartbeat();
    
    // 创建新的心跳定时器
    _heartbeatTimer = Timer.periodic(Duration(milliseconds: _config.heartbeatIntervalMs), (timer) {
      _sendHeartbeat();
    });
    
    LogUtil.debug('已启动WebSocket心跳，间隔: ${_config.heartbeatIntervalMs}ms');
  }
  
  /// 取消心跳
  void _cancelHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = null;
    
    _heartbeatTimeoutTimer?.cancel();
    _heartbeatTimeoutTimer = null;
    
    _missedHeartbeats = 0;
  }
  
  /// 发送心跳
  void _sendHeartbeat() {
    _heartbeatLock.synchronized(() {
      try {
        if (!isConnected) {
          LogUtil.warn('WebSocket未连接，跳过心跳');
          return;
        }
        
        // 获取当前时间戳
        final timestamp = DateTime.now().millisecondsSinceEpoch;
        
        // 发送ping
        final pingMessage = json.encode({
          'event': 'ping',
          'data': timestamp
        });
        
        LogUtil.debug('发送心跳ping: $timestamp, 当前未响应心跳数: $_missedHeartbeats');
        send(pingMessage);
        
        // 增加未响应心跳计数
        _missedHeartbeats++;
        
        // 如果超过最大未响应心跳数，认为连接已断开
        if (_missedHeartbeats > _maxMissedHeartbeats) {
          // 增加网络状况检查，避免在网络正常但服务器响应慢时断开连接
          LogUtil.warn('连续${_missedHeartbeats}次心跳未收到响应，执行连接状态检查');
          _checkConnectionBeforeDisconnect(timestamp);
          return;
        }
        
        // 设置心跳超时定时器
        _heartbeatTimeoutTimer?.cancel();
        _heartbeatTimeoutTimer = Timer(Duration(milliseconds: _config.heartbeatTimeoutMs), () {
          LogUtil.warn('心跳响应超时 - 时间戳: $timestamp');
          
          // 增强的心跳超时处理：第一次心跳超时就执行快速连接检查
          // 而不是等待多次心跳失败
          _performQuickConnectionCheck(timestamp);
        });
      } catch (e) {
        LogUtil.error('发送心跳失败: $e');
        // 记录更详细的错误信息
        LogUtil.error('心跳错误详情: ${e.toString()}');
      }
    });
  }
  
  /// 执行快速连接检查
  /// 
  /// 在心跳超时时执行更快的连接检查，不等待多次心跳失败
  /// [timestamp] 本次心跳的时间戳
  void _performQuickConnectionCheck(int timestamp) {
    try {
      // 如果已经不是连接状态，不执行检查
      if (!isConnected) {
        LogUtil.debug('连接已断开，跳过快速连接检查');
        return;
      }
      
      // 检查网络状态
      _isNetworkConnected().then((isConnected) {
        if (!isConnected) {
          // 网络连接已断开，更新状态并等待网络恢复
          LogUtil.warn('心跳超时且网络不可用，将连接状态更新为等待网络恢复');
          _updateState(WsConnectionState.waitingForNetwork);
          
          // 触发连接断开处理
          _handleDisconnect('心跳超时且网络不可用');
          return;
        }
        
        // 网络连接正常，发送额外的ping进行连接检查
        LogUtil.info('心跳超时但网络正常，发送额外ping进行连接检查');
        
        // 发送特殊的心跳包进行检查
        final checkPingMessage = json.encode({
          'event': 'ping',
          'data': timestamp,
          'check_type': 'quick_timeout_check'
        });
        send(checkPingMessage);
        
        // 设置一个短时间的检查定时器
        final quickCheckTimeout = _config.heartbeatTimeoutMs ~/ 2;
        Timer(Duration(milliseconds: quickCheckTimeout), () {
          // 如果心跳仍然未重置，认为连接可能有问题
          if (_missedHeartbeats > 0) {
            LogUtil.warn('快速连接检查失败，心跳未响应，触发连接重建');
            _handleDisconnect('心跳快速检查失败');
          } else {
            LogUtil.info('快速连接检查通过，心跳已恢复正常');
          }
        });
      }).catchError((error) {
        LogUtil.error('网络状态检查失败: $error');
        // 如果网络检查失败，为安全起见执行常规的连接检查
        _checkConnectionBeforeDisconnect(timestamp);
      });
    } catch (e) {
      LogUtil.error('执行快速连接检查失败: $e');
      // 如果检查过程出错，为安全起见执行常规的连接检查
      _checkConnectionBeforeDisconnect(timestamp);
    }
  }
  
  /// 在断开连接前检查连接状态
  /// 
  /// 通过发送一个额外的ping消息来验证连接是否真的断开，
  /// 以避免因为临时网络波动导致不必要的断开连接
  void _checkConnectionBeforeDisconnect(int timestamp) {
    try {
      // 如果已经不是连接状态，直接处理断开
      if (!isConnected) {
        _handleDisconnect('心跳超时且连接已断开');
        return;
      }
      
      LogUtil.info('执行连接状态最终检查，延迟断开决定');
      
      // 发送最后一次ping尝试
      final finalPingMessage = json.encode({
        'event': 'ping',
        'data': timestamp,
        'final_check': true
      });
      
      send(finalPingMessage);
      
      // 给服务器额外的响应时间
      final extraTimeoutMs = _config.heartbeatTimeoutMs ~/ 2;
      Timer(Duration(milliseconds: extraTimeoutMs), () {
        // 如果在额外等待时间内收到了pong，_missedHeartbeats会被重置
        // 否则，认为连接确实断开了
        if (_missedHeartbeats > _maxMissedHeartbeats) {
          LogUtil.warn('最终连接检查失败，确认心跳超时，断开连接');
          _handleDisconnect('心跳最终检查超时');
        } else {
          LogUtil.info('最终连接检查通过，保持连接');
        }
      });
    } catch (e) {
      LogUtil.error('连接状态检查失败: $e');
      // 如果检查过程中出错，为安全起见断开连接
      _handleDisconnect('心跳检查过程出错');
    }
  }
  
  // 处理pong消息的标志位，避免并发处理
  bool _isHandlingPong = false;
  
  /// 处理心跳响应 - 使用简单的布尔标志位替代synchronized锁
  void _handlePong() {
    // 如果已经在处理中，直接返回
    if (_isHandlingPong) {
      return;
    }
    
    _isHandlingPong = true;
    
    try {
      LogUtil.debug('处理pong消息 - 当前未响应心跳数: $_missedHeartbeats');
      
      // 重置未响应心跳计数
      _missedHeartbeats = 0;
      
      // 取消心跳超时定时器
      _heartbeatTimeoutTimer?.cancel();
      _heartbeatTimeoutTimer = null;
      
      // 记录最后一次收到pong的时间
      _lastPongTime = DateTime.now();
      
      LogUtil.debug('心跳状态已重置，最后pong时间: $_lastPongTime');
    } finally {
      _isHandlingPong = false;
    }
  }
  
  /// 处理连接错误
  void _handleConnectionError(Object error) {
    LogUtil.error('WebSocket连接错误: $error，用户ID: $_userId');
    _handleDisconnect(error);
  }

  /// 处理连接关闭
  void _handleConnectionClosed() {
    LogUtil.info('WebSocket连接关闭，用户ID: $_userId');
    _handleDisconnect(null);
  }
  
  /// 处理连接断开
  void _handleDisconnect(dynamic error) {
    // 取消心跳
    _cancelHeartbeat();
    
    // 清理资源
    _channel = null;
    
    // 更新状态
    if (error != null) {
      _updateState(WsConnectionState.failed);
      LogUtil.error('WebSocket连接失败: $error');
      
      // 发送WebSocket断开事件
      try {
        GlobalEventState.to.triggerWebsocketDisconnected(
          error: error.toString(),
          state: 'failed'
        );
      } catch (e) {
        LogUtil.error('发送WebSocket断开事件失败: $e');
      }
      
      // 只有在配置允许自动重连且用户ID不为空时才尝试重连
      if (_config.autoReconnect && _userId != null && _userId!.isNotEmpty) {
        LogUtil.info('连接失败，尝试自动重连，用户ID: $_userId');
        _scheduleReconnect();
      } else {
        LogUtil.info('连接失败，自动重连已禁用或用户未登录，不进行重连');
      }
    } else {
      _updateState(WsConnectionState.disconnected);
      LogUtil.info('WebSocket连接已断开');
      
      // 发送WebSocket断开事件
      try {
        GlobalEventState.to.triggerWebsocketDisconnected(
          state: 'disconnected'
        );
      } catch (e) {
        LogUtil.error('发送WebSocket断开事件失败: $e');
      }
      
      // 只有在用户主动断开以外的情况下，才考虑自动重连
      if (_config.autoReconnect && _userId != null && _userId!.isNotEmpty) {
        LogUtil.info('连接断开，尝试自动重连，用户ID: $_userId');
        _scheduleReconnect();
      } else {
        LogUtil.info('连接断开，不进行重连，用户ID: ${_userId ?? '未登录'}');
      }
    }
  }
  
  /// 设置网络状态监听器
  void _setupNetworkListener() {
    try {
      _networkSubscription = Connectivity().onConnectivityChanged.listen((List<ConnectivityResult> result) {
        final previousState = _isNetworkAvailable;
        _isNetworkAvailable = (result.isNotEmpty && result.first != ConnectivityResult.none);
        
        LogUtil.info('网络状态变化: $result, 网络可用: $_isNetworkAvailable');
        
        // 网络从不可用变为可用时，如果WebSocket未连接，尝试重连
        if (!previousState && _isNetworkAvailable && 
            (connectionState == WsConnectionState.disconnected || 
             connectionState == WsConnectionState.failed || 
             connectionState == WsConnectionState.waitingForNetwork)) {
          LogUtil.info('网络恢复连接，尝试重新连接WebSocket');
          
          // 重置重连尝试次数，给予新的重连机会
          _reconnectAttempts = 0;
          
          // 延迟一段时间后尝试重连，确保网络稳定
          Timer(Duration(milliseconds: 1000), () {
            _attemptReconnect();
          });
        }
      });
      
      // 初始化时检查一次当前网络状态
      Connectivity().checkConnectivity().then((List<ConnectivityResult> result) {
        _isNetworkAvailable = (result.isNotEmpty && result.first != ConnectivityResult.none);
        LogUtil.debug('初始网络状态检查: $result, 网络可用: $_isNetworkAvailable');
      });
      
      LogUtil.debug('已设置网络状态监听器');
    } catch (e) {
      LogUtil.error('设置网络状态监听器失败: $e');
    }
  }
  
  /// 检查当前网络是否可用
  Future<bool> _isNetworkConnected() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      final isConnected = connectivityResult.isNotEmpty && 
                          !connectivityResult.contains(ConnectivityResult.none);
      LogUtil.debug('网络连接检查: $connectivityResult, 网络可用: $isConnected');
      return isConnected;
    } catch (e) {
      LogUtil.error('检查网络连接状态失败: $e');
      // 默认返回true，避免因为检查失败而阻止重连
      return true;
    }
  }

  /// 安排重连
  void _scheduleReconnect() {
    _reconnectLock.synchronized(() async {
      // 如果已经有重连定时器，不重复创建
      if (_reconnectTimer != null) {
        return;
      }
      
      // 检查用户ID是否存在，如果不存在则不进行重连
      if (_userId == null || _userId!.isEmpty) {
        LogUtil.info('用户未登录或用户ID为空，跳过自动重连');
        return;
      }
      
      // 检查是否达到最大重连次数
      if (_reconnectAttempts >= _maxReconnectAttempts) {
        LogUtil.warn('已达到最大重连次数($_maxReconnectAttempts)，停止自动重连');
        _reconnectStatus.value = ReconnectStatus(
          isReconnecting: false,
          attempt: _reconnectAttempts,
          delayMs: 0,
          maxAttempts: _maxReconnectAttempts,
        );
        return;
      }
      
      // 检查网络连接状态
      final isNetworkConnected = await _isNetworkConnected();
      if (!isNetworkConnected) {
        LogUtil.warn('网络连接不可用，暂停重连。等待网络恢复后将自动尝试重连');

        // 通知UI网络不可用
        _reconnectStatus.value = ReconnectStatus(
          isReconnecting: false,
          attempt: _reconnectAttempts,
          delayMs: 0,
          maxAttempts: _maxReconnectAttempts,
          networkAvailable: false
        );
        
        // 设置连接状态为等待网络
        _updateState(WsConnectionState.waitingForNetwork);
        
        // 注意：不设置重连定时器，依靠网络状态监听器在网络恢复时重连
        return;
      }
      
      // 计算重连延迟
      final delay = _calculateReconnectDelay();
      
      // 通知重连状态
      _reconnectStatus.value = ReconnectStatus(
        isReconnecting: true,
        attempt: _reconnectAttempts + 1,
        delayMs: delay,
        maxAttempts: _maxReconnectAttempts,
        networkAvailable: true
      );
      
      LogUtil.info('计划在${delay}ms后重连，尝试次数: ${_reconnectAttempts + 1}/$_maxReconnectAttempts');
      
      // 创建重连定时器
      _reconnectTimer = Timer(Duration(milliseconds: delay), () {
        _reconnectTimer = null;
        _attemptReconnect();
      });
    });
  }
  
  /// 取消重连
  void _cancelReconnect() {
    _reconnectLock.synchronized(() {
      _reconnectTimer?.cancel();
      _reconnectTimer = null;
      
      // 通知重连状态
      _reconnectStatus.value = ReconnectStatus(
        isReconnecting: false,
        attempt: 0,
        delayMs: 0,
        networkAvailable: _isNetworkAvailable
      );
    });
  }
  
  /// 尝试重连
  Future<void> _attemptReconnect() async {
    return _reconnectLock.synchronized(() async {
      // 再次检查URL和用户ID
      if (_wsUrl == null) {
        LogUtil.warn('没有可用的WebSocket URL，无法重连');
        return;
      }
      
      // 再次检查用户ID是否存在
      if (_userId == null || _userId!.isEmpty) {
        LogUtil.warn('用户未登录或用户ID为空，取消重连');
        return;
      }
      
      // 检查是否达到最大重连次数
      if (_reconnectAttempts >= _maxReconnectAttempts) {
        LogUtil.warn('已达到最大重连次数($_maxReconnectAttempts)，停止重连');
        _reconnectStatus.value = ReconnectStatus(
          isReconnecting: false,
          attempt: _reconnectAttempts,
          delayMs: 0,
          maxAttempts: _maxReconnectAttempts,
        );
        return;
      }
      
      LogUtil.info('尝试重连WebSocket: $_wsUrl，用户ID: $_userId');
      
      // 增加重连尝试次数
      _reconnectAttempts++;
      
      // 尝试连接
      final success = await connect(_wsUrl!, userId: _userId);
      
      if (success) {
        LogUtil.info('WebSocket重连成功');
        
        // 通知重连状态
        _reconnectStatus.value = ReconnectStatus(
          isReconnecting: false,
          attempt: _reconnectAttempts,
          delayMs: 0,
          success: true,
          maxAttempts: _maxReconnectAttempts,
        );
        
        // 重置重连尝试次数
        _reconnectAttempts = 0;
      } else {
        LogUtil.warn('WebSocket重连失败(${_reconnectAttempts}/$_maxReconnectAttempts)，将再次尝试');
        
        // 安排下一次重连
        _scheduleReconnect();
      }
    });
  }
  
  /// 计算重连延迟
  int _calculateReconnectDelay() {
    // 使用指数退避算法计算重连延迟
    final baseDelay = _config.reconnectBaseDelayMs;
    final maxDelay = _reconnectMaxDelayMs;
    
    // 计算延迟，添加随机抖动
    final delay = min(baseDelay * pow(2, _reconnectAttempts), maxDelay).toInt();
    final jitter = Random().nextInt(delay ~/ 4);
    
    return delay + jitter;
  }
  
  /// 手动重连WebSocket
  /// 
  /// 重置重连计数并尝试重新连接，用于用户手动触发重连
  Future<bool> manualReconnect() async {
    LogUtil.info('用户手动触发重连，重置重连计数');
    
    // 重置重连尝试次数
    _reconnectAttempts = 0;
    
    // 取消现有的重连定时器
    _cancelReconnect();
    
    // 尝试重新连接
    return await reconnect();
  }
  
  /// 重新连接WebSocket
  /// 
  /// 使用最后一次成功的连接参数尝试重新连接
  Future<bool> reconnect() async {
    try {
      // 如果没有URL，无法重连
      if (_wsUrl == null) {
        LogUtil.warn('没有可用的WebSocket URL，无法重连');
        return false;
      }
      
      // 检查用户ID是否存在
      if (_userId == null || _userId!.isEmpty) {
        LogUtil.warn('用户未登录或用户ID为空，无法重连');
        return false;
      }

      // 检查是否达到最大重连次数
      if (_reconnectAttempts >= _maxReconnectAttempts) {
        LogUtil.warn('已达到最大重连次数($_maxReconnectAttempts)，无法手动重连');
        _reconnectStatus.value = ReconnectStatus(
          isReconnecting: false,
          attempt: _reconnectAttempts,
          delayMs: 0,
          maxAttempts: _maxReconnectAttempts,
        );
        return false;
      }

      // 计算指数退避延迟
      final delayMs = min(
        _config.reconnectBaseDelayMs * pow(2, _reconnectAttempts), 
        _reconnectMaxDelayMs
      ).toInt();
      
      // 增加重连尝试计数
      _reconnectAttempts++;
      
      // 更新状态，通知重连开始
      _updateState(WsConnectionState.reconnecting);
      _reconnectStatus.value = ReconnectStatus(
        isReconnecting: true,
        attempt: _reconnectAttempts,
        delayMs: delayMs,
        maxAttempts: _maxReconnectAttempts,
      );
      
      LogUtil.info('尝试重连 (第 $_reconnectAttempts/$_maxReconnectAttempts 次)，延迟: ${delayMs}ms，用户ID: $_userId');
      
      // 延迟重连
      await Future.delayed(Duration(milliseconds: delayMs));
      
      // 如果应用处于后台，推迟重连
      if (_appState != AppLifecycleState.resumed) {
        LogUtil.info('应用当前在后台，推迟重连直到应用回到前台');
        _updateState(WsConnectionState.waitingForResume);
        return false;
      }
      
      // 尝试重新连接
      // 注意：由于token在URL中，我们不需要额外的headers
      // 如果URL中的token已过期，连接会失败，应由上层处理构建新的URL
      final result = await connect(_wsUrl!, userId: _userId);
      
      if (result) {
        // 重连成功，重置重连计数
        _reconnectAttempts = 0;
      }
      
      return result;
    } catch (e) {
      LogUtil.error('WebSocket重连失败: $e');
      _updateState(WsConnectionState.failed);
      return false;
    }
  }
  
  /// 应用生命周期变化回调
  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    _appState = state;
    
    switch (state) {
      case AppLifecycleState.resumed:
        LogUtil.debug('应用恢复前台，检查WebSocket连接');
        _handleAppResumed();
        break;
      case AppLifecycleState.paused:
        LogUtil.debug('应用进入后台');
        break;
      default:
        // 其他状态不处理
        break;
    }
  }
  
  /// 处理应用恢复前台
  void _handleAppResumed() {
    // 如果连接已断开、失败或等待网络，尝试重连
    if (connectionState == WsConnectionState.disconnected || 
        connectionState == WsConnectionState.failed ||
        connectionState == WsConnectionState.waitingForNetwork) {
      LogUtil.info('应用恢复前台，WebSocket连接状态为$connectionState，尝试重连');
      
      // 如果状态是waitingForNetwork，先检查网络是否已经恢复
      if (connectionState == WsConnectionState.waitingForNetwork) {
        _isNetworkConnected().then((isConnected) {
          if (isConnected) {
            LogUtil.info('网络已恢复连接，应用前台时尝试重连');
            // 重置重连尝试次数，给予新的重连机会
            _reconnectAttempts = 0;
            _attemptReconnect();
          } else {
            LogUtil.info('网络仍然不可用，即使应用恢复前台也不尝试重连');
            // 保持等待网络状态
          }
        });
      } else {
        _attemptReconnect();
      }
    } 
    // 如果连接状态正常，但长时间没有收到心跳响应，也尝试重连
    else if (isConnected && _lastPongTime != null) {
      final elapsed = DateTime.now().difference(_lastPongTime!).inSeconds;
      if (elapsed > _config.heartbeatIntervalMs ~/ 1000 * 2) {
        LogUtil.warn('应用恢复前台，WebSocket连接可能已断开（${elapsed}秒未收到心跳响应），尝试重连');
        disconnect().then((_) => _attemptReconnect());
      }
    }
  }
  
  @override
  void onClose() {
    // 取消网络状态监听
    _networkSubscription?.cancel();

    // 取消心跳
    _cancelHeartbeat();

    // 取消重连
    _cancelReconnect();

    // 断开连接
    disconnect();

    // 取消应用生命周期监听
    WidgetsBinding.instance.removeObserver(this);

    super.onClose();
    LogUtil.debug('WsConnectionManager已销毁');
  }
}