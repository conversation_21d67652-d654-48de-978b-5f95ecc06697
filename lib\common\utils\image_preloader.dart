import 'dart:async';
import 'dart:collection';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/common/cache/cache_manager.dart';
import 'package:get/get.dart';

/// 图片预加载优先级
enum ImagePreloadPriority {
  /// 高优先级 - 立即加载
  high,
  
  /// 中优先级 - 当高优先级加载完成后加载
  medium,
  
  /// 低优先级 - 仅在空闲时加载
  low
}

/// 图片预加载质量
enum ImagePreloadQuality {
  /// 原始质量
  original,
  
  /// 高质量
  high,
  
  /// 中等质量
  medium,
  
  /// 低质量 (缩略图)
  low
}

/// 图片预加载项
class PreloadItem {
  final String url;
  final ImagePreloadPriority priority;
  final ImagePreloadQuality quality;
  final int? width;
  final int? height;
  final double score; // 预测分数，用于确定加载顺序
  final Function(bool success)? onComplete; // 加载完成回调

  PreloadItem({
    required this.url,
    this.priority = ImagePreloadPriority.medium,
    this.quality = ImagePreloadQuality.original,
    this.width,
    this.height,
    this.score = 0.0,
    this.onComplete,
  });
}

/// 图片预加载工具类
/// 
/// 用于提前加载图片资源，减少用户等待时间
class ImagePreloader extends GetxService {
  /// 单例实例
  static final ImagePreloader _instance = ImagePreloader._internal();
  
  /// 已预加载的图片URL集合
  final Map<String, bool> _preloadedImages = <String, bool>{};
  
  /// 正在加载的图片URL集合
  final Set<String> _loadingImages = <String>{};
  
  /// 最大并发加载数
  final int _maxConcurrentLoads;
  
  /// 优先级队列 - 高优先级
  final Queue<PreloadItem> _highPriorityQueue = Queue<PreloadItem>();
  
  /// 优先级队列 - 中优先级
  final Queue<PreloadItem> _mediumPriorityQueue = Queue<PreloadItem>();
  
  /// 优先级队列 - 低优先级
  final Queue<PreloadItem> _lowPriorityQueue = Queue<PreloadItem>();
  
  /// 网络状态
  bool _isOnWifi = true; // 默认假设在WiFi环境
  
  /// 设备内存压力指示器
  bool _isLowMemory = false;
  
  /// 是否正在处理队列
  bool _isProcessingQueue = false;
  
  /// 缓存管理器
  late final CacheManager _cacheManager;
  
  /// 去重集合 - 记录短时间内处理过的URL，避免重复加载
  final Map<String, int> _recentlyProcessed = <String, int>{};
  
  /// 防抖定时器
  Timer? _debounceTimer;

  /// 清理定时器
  Timer? _cleanupTimer;

  /// 队列处理定时器
  Timer? _queueProcessingTimer;

  /// 批处理触发器 - 用于GetX响应式批处理
  final RxInt _batchTrigger = 0.obs;

  /// 清理触发器 - 用于GetX响应式清理
  final RxInt _cleanupTrigger = 0.obs;

  /// 队列处理触发器 - 用于GetX响应式队列处理
  final RxInt _queueTrigger = 0.obs;

  /// 批处理队列 - 用于合并多次预加载请求
  final List<PreloadItem> _batchQueue = [];

  /// 批处理锁 - 防止并发访问
  bool _isBatchProcessing = false;
  
  /// 构造函数
  factory ImagePreloader() {
    return _instance;
  }
  
  /// 内部构造函数
  ImagePreloader._internal() : _maxConcurrentLoads = 8 { // 增加并发加载数量
    _cacheManager = Get.find<CacheManager>();
    _detectNetworkType();
  }

  @override
  void onInit() {
    super.onInit();
    _setupPeriodicProcessing();
    _setupCleanupTask();
    _setupBatchProcessing();
    LogUtil.debug('ImagePreloader初始化完成');
  }

  /// 设置批处理防抖机制
  void _setupBatchProcessing() {
    // 使用GetX的debounce替代Timer防抖
    debounce(_batchTrigger, (value) => _processBatchQueue(), time: const Duration(milliseconds: 100));
  }

  @override
  void onClose() {
    // 清理所有定时器和资源
    _debounceTimer?.cancel();
    _cleanupTimer?.cancel();
    _queueProcessingTimer?.cancel();
    super.onClose();
    LogUtil.debug('ImagePreloader资源清理完成');
  }

  /// 设置清理任务 - 定期清理去重缓存
  void _setupCleanupTask() {
    // 使用GetX的interval替代Timer.periodic
    interval(_cleanupTrigger, (value) => _cleanupRecentlyProcessed(), time: const Duration(minutes: 1));
  }
  
  /// 清理最近处理过的URL缓存
  void _cleanupRecentlyProcessed() {
    if (_recentlyProcessed.isEmpty) return;
    
    final now = DateTime.now().millisecondsSinceEpoch;
    final expiredKeys = <String>[];
    
    // 查找超过1分钟的记录
    _recentlyProcessed.forEach((url, timestamp) {
      if (now - timestamp > 60000) { // 1分钟
        expiredKeys.add(url);
      }
    });
    
    // 移除过期记录
    for (final key in expiredKeys) {
      _recentlyProcessed.remove(key);
    }
    
    if (expiredKeys.isNotEmpty) {
      LogUtil.debug('已清理${expiredKeys.length}条预加载URL去重缓存');
    }
  }

  /// 设置定期处理队列
  void _setupPeriodicProcessing() {
    // 使用GetX的interval替代Timer.periodic
    interval(_queueTrigger, (value) {
      if (!_isProcessingQueue && _loadingImages.length < _maxConcurrentLoads) {
        _processQueue();
      }
    }, time: const Duration(seconds: 1));
  }
  
  /// 检测网络类型 (WiFi或移动网络)
  void _detectNetworkType() {
    // 在实际实现中应使用连接检测包如connectivity_plus
    // 这里简化实现
    _isOnWifi = true;
  }
  
  /// 根据网络类型和内存状态确定最大并发数
  int get _effectiveMaxConcurrentLoads {
    if (_isLowMemory) {
      return 2; // 内存压力大时减少并发
    }
    return _isOnWifi ? _maxConcurrentLoads : 3; // WiFi下并发更多，移动网络少一些
  }
  
  /// 预加载单个图片
  /// 
  /// [url] 图片URL
  /// [width] 图片宽度
  /// [height] 图片高度
  /// [priority] 加载优先级
  /// [quality] 加载质量
  /// [onComplete] 加载完成回调
  Future<void> preloadImage(
    String url, {
    int? width,
    int? height, 
    ImagePreloadPriority priority = ImagePreloadPriority.medium,
    ImagePreloadQuality quality = ImagePreloadQuality.original,
    Function(bool success)? onComplete
  }) async {
    if (url.isEmpty) {
      LogUtil.warn('预加载图片失败：URL为空');
      if (onComplete != null) onComplete(false);
      return;
    }
    
    // 生成缓存键，包含质量信息
    final cacheKey = _generateCacheKey(url, quality, width, height);
    
    // 检查是否在短时间内已处理过此URL
    final now = DateTime.now().millisecondsSinceEpoch;
    if (_recentlyProcessed.containsKey(cacheKey) && now - _recentlyProcessed[cacheKey]! < 1000) {
      // 如果在最近1秒内已处理过，跳过此次处理
      LogUtil.debug('跳过重复预加载请求: $cacheKey (重复间隔: ${now - _recentlyProcessed[cacheKey]!}ms)');
      if (onComplete != null) onComplete(true);
      return;
    }
    
    // 记录此次处理时间
    _recentlyProcessed[cacheKey] = now;
    
    // 如果已经加载过，直接返回成功
    if (_preloadedImages.containsKey(cacheKey)) {
      if (onComplete != null) onComplete(true);
      return;
    }
    
    // 如果正在加载，不重复加载
    if (_loadingImages.contains(cacheKey)) {
      return;
    }
    
    // 创建预加载项
    final item = PreloadItem(
      url: url,
      priority: priority,
      quality: quality,
      width: width,
      height: height,
      score: _calculateItemScore(url, priority),
      onComplete: onComplete,
    );
    
    // 高优先级图片立即加载,不进入队列
    if (priority == ImagePreloadPriority.high) {
      _loadImageItem(item);
      return;
    }
    
    // 其他优先级图片进入队列
    switch (priority) {
      case ImagePreloadPriority.high:
        // 高优先级在前面已经处理
        break;
      case ImagePreloadPriority.medium:
        _mediumPriorityQueue.add(item);
        break;
      case ImagePreloadPriority.low:
        _lowPriorityQueue.add(item);
        break;
    }
    
    // 如果当前加载数量低于最大并发数，处理队列
    if (_loadingImages.length < _effectiveMaxConcurrentLoads) {
      _processQueue();
    }
  }
  
  /// 计算预加载项的优先级分数
  double _calculateItemScore(String url, ImagePreloadPriority priority) {
    double baseScore;
    switch (priority) {
      case ImagePreloadPriority.high:
        baseScore = 100.0;
        break;
      case ImagePreloadPriority.medium:
        baseScore = 50.0;
        break;
      case ImagePreloadPriority.low:
        baseScore = 10.0;
        break;
    }
    
    // 可以基于历史访问模式、屏幕位置等调整分数
    // 这里使用一个简单的实现
    return baseScore;
  }
  
  /// 生成缓存键
  String _generateCacheKey(String url, ImagePreloadQuality quality, int? width, int? height) {
    if (quality == ImagePreloadQuality.original) {
      return url;
    }
    
    return '$url#quality=${quality.toString()}#width=${width ?? "auto"}#height=${height ?? "auto"}';
  }
  
  /// 基于质量级别获取调整后的URL
  /// 在实际应用中可以连接到图片调整服务如Cloudinary或本地图片服务
  String _getQualityAdjustedUrl(String url, ImagePreloadQuality quality, int? width, int? height) {
    // 简化实现 - 在实际应用中应替换为真正的图片优化服务调用
    switch (quality) {
      case ImagePreloadQuality.low:
        // 例如：使用缩略图URL
        return url; // 实际应用中这里应返回缩略图URL
      case ImagePreloadQuality.medium:
        return url; // 中等质量URL
      case ImagePreloadQuality.high:
      case ImagePreloadQuality.original:
        return url; // 原始URL
    }
  }
  
  /// 预加载多个图片
  /// 
  /// [urls] 图片URL列表
  /// [width] 图片宽度
  /// [height] 图片高度
  /// [priority] 加载优先级
  /// [quality] 加载质量
  Future<void> preloadImages(
    List<String> urls, {
    int? width,
    int? height,
    ImagePreloadPriority priority = ImagePreloadPriority.medium,
    ImagePreloadQuality quality = ImagePreloadQuality.original
  }) async {
    if (urls.isEmpty) {
      return;
    }
    
    // 过滤掉无效URL和重复URL
    final validUrls = urls
        .where((url) => url.isNotEmpty)
        .toSet() // 使用Set去重
        .toList();
    
    if (validUrls.isEmpty) {
      return;
    }
    
    // 记录添加到队列的数量，而非重复调用日志
    LogUtil.debug('添加${validUrls.length}张图片到预加载队列，优先级: $priority');
    
    // 将图片添加到批处理队列
    for (final url in validUrls) {
      _batchQueue.add(
        PreloadItem(
          url: url,
          width: width,
          height: height,
          priority: priority,
          quality: quality
        )
      );
    }

    // 触发GetX防抖处理
    _batchTrigger.value++;
  }
  
  /// 处理批次队列
  void _processBatchQueue() async {
    if (_isBatchProcessing || _batchQueue.isEmpty) return;
    
    _isBatchProcessing = true;
    
    try {
      // 创建URL去重集合
      final uniqueUrls = <String>{};
      final batch = <PreloadItem>[];
      
      // 过滤重复URL
      for (final item in _batchQueue) {
        final cacheKey = _generateCacheKey(item.url, item.quality, item.width, item.height);
        if (!uniqueUrls.contains(cacheKey)) {
          uniqueUrls.add(cacheKey);
          batch.add(item);
        }
      }
      
      // 清空批次队列
      _batchQueue.clear();
      
      // 记录实际添加的图片数量
      final filteredCount = batch.length;
      if (filteredCount > 0 && filteredCount < uniqueUrls.length) {
        LogUtil.debug('过滤后实际预加载${filteredCount}张唯一图片（移除了${uniqueUrls.length - filteredCount}张重复图片）');
      }
      
      // 处理批次中的每个图片
      for (final item in batch) {
        await preloadImage(
          item.url,
          width: item.width,
          height: item.height,
          priority: item.priority,
          quality: item.quality
        );
      }
    } finally {
      _isBatchProcessing = false;
    }
  }
  
  /// 智能预加载 - 基于用户可能需要的图片
  /// 
  /// [visibleUrls] 当前可见的图片URL列表
  /// [potentialUrls] 可能即将看到的图片URL列表
  Future<void> smartPreload({
    required List<String> visibleUrls,
    required List<String> potentialUrls,
  }) async {
    // 可见的图片高优先级加载
    await preloadImages(
      visibleUrls,
      priority: ImagePreloadPriority.high,
      quality: _isOnWifi ? ImagePreloadQuality.original : ImagePreloadQuality.high,
    );
    
    // 可能看到的图片中优先级加载
    await preloadImages(
      potentialUrls,
      priority: ImagePreloadPriority.medium,
      quality: _isOnWifi ? ImagePreloadQuality.high : ImagePreloadQuality.medium,
    );
  }
  
  /// 处理加载队列
  Future<void> _processQueue() async {
    if (_isProcessingQueue) {
      return;
    }
    
    _isProcessingQueue = true;
    
    try {
      // 如果队列为空，直接返回
      if (_highPriorityQueue.isEmpty && _mediumPriorityQueue.isEmpty && _lowPriorityQueue.isEmpty) {
        return;
      }
      
      // 计算可以加载的数量
      final availableSlots = _effectiveMaxConcurrentLoads - _loadingImages.length;
      
      // 如果没有可用槽位，直接返回
      if (availableSlots <= 0) {
        return;
      }
      
      int slotsUsed = 0;
      
      // 优先处理高优先级队列
      while (slotsUsed < availableSlots && _highPriorityQueue.isNotEmpty) {
        final item = _highPriorityQueue.removeFirst();
        await _loadImageItem(item);
        slotsUsed++;
      }
      
      // 处理中优先级队列
      while (slotsUsed < availableSlots && _mediumPriorityQueue.isNotEmpty) {
        final item = _mediumPriorityQueue.removeFirst();
        await _loadImageItem(item);
        slotsUsed++;
      }
      
      // 如果还有空闲槽位且不在移动网络上，处理低优先级队列
      if (_isOnWifi) {
        while (slotsUsed < availableSlots && _lowPriorityQueue.isNotEmpty) {
          final item = _lowPriorityQueue.removeFirst();
          await _loadImageItem(item);
          slotsUsed++;
        }
      }
    } finally {
      _isProcessingQueue = false;
    }
  }
  
  /// 加载单个图片项
  Future<void> _loadImageItem(PreloadItem item) async {
    final cacheKey = _generateCacheKey(item.url, item.quality, item.width, item.height);
    final adjustedUrl = _getQualityAdjustedUrl(item.url, item.quality, item.width, item.height);
    
    if (_preloadedImages.containsKey(cacheKey) || _loadingImages.contains(cacheKey)) {
      return;
    }
    
    try {
      _loadingImages.add(cacheKey);
      
      // 首先检查缓存中是否已有此图片
      final bool existsInCache = await _checkImageInCache(cacheKey);
      if (existsInCache) {
        _preloadedImages[cacheKey] = true;
        _loadingImages.remove(cacheKey);
        if (item.onComplete != null) {
          item.onComplete!(true);
        }
        _processQueue();
        return;
      }
      
      // 使用CachedNetworkImage的ImageProvider预加载
      final provider = CachedNetworkImageProvider(adjustedUrl);
      
      // 创建ImageConfiguration
      final configuration = ImageConfiguration(
        size: item.width != null && item.height != null 
            ? Size(item.width!.toDouble(), item.height!.toDouble()) 
            : null,
      );
      
      // 预加载图片
      final imageStream = provider.resolve(configuration);
      final completer = Completer<void>();
      
      late ImageStreamListener listener;
      listener = ImageStreamListener(
        (ImageInfo imageInfo, bool synchronousCall) {
          _preloadedImages[cacheKey] = true;
          _loadingImages.remove(cacheKey);
          imageStream.removeListener(listener);
          completer.complete();
          _processQueue();
          if (item.onComplete != null) {
            item.onComplete!(true);
          }
        },
        onError: (dynamic exception, StackTrace? stackTrace) {
          _loadingImages.remove(cacheKey);
          LogUtil.error('图片预加载失败: $cacheKey, 错误: $exception');
          imageStream.removeListener(listener);
          completer.completeError(exception);
          _processQueue();
          if (item.onComplete != null) {
            item.onComplete!(false);
          }
        },
      );
      
      imageStream.addListener(listener);
      return completer.future;
    } catch (e) {
      _loadingImages.remove(cacheKey);
      LogUtil.error('图片预加载异常: $cacheKey, 错误: $e');
      _processQueue();
      if (item.onComplete != null) {
        item.onComplete!(false);
      }
    }
  }
  
  /// 检查图片是否已在缓存中
  Future<bool> _checkImageInCache(String cacheKey) async {
    // 使用CacheManager检查图片是否已缓存
    try {
      final exists = await _cacheManager.exists(
        'image_$cacheKey', 
        strategy: CacheStrategy.memoryThenPersistent
      );
      return exists;
    } catch (e) {
      return false;
    }
  }
  
  /// 清除预加载缓存
  void clearMemoryCache() {
    _preloadedImages.clear();
    _highPriorityQueue.clear();
    _mediumPriorityQueue.clear();
    _lowPriorityQueue.clear();
    _batchQueue.clear();
    _recentlyProcessed.clear();
    LogUtil.debug('已清除图片预加载内存缓存');
  }
  
  /// 清除所有缓存(内存和持久化)
  Future<void> clearAllCache() async {
    clearMemoryCache();
    try {
      await _cacheManager.clear(strategy: CacheStrategy.both);
      LogUtil.debug('已清除所有图片缓存');
    } catch (e) {
      LogUtil.error('清除图片缓存失败: $e');
    }
  }
  
  /// 检查图片是否已预加载
  bool isImagePreloaded(String url) {
    // 检查原始URL
    if (_preloadedImages.containsKey(url)) {
      return true;
    }
    
    // 检查各种质量级别
    for (var quality in ImagePreloadQuality.values) {
      final cacheKey = _generateCacheKey(url, quality, null, null);
      if (_preloadedImages.containsKey(cacheKey)) {
        return true;
      }
    }
    
    return false;
  }
  
  /// 获取已预加载的图片数量
  int get preloadedCount => _preloadedImages.length;
  
  /// 获取正在加载的图片数量
  int get loadingCount => _loadingImages.length;
  
  /// 获取队列中的图片数量
  int get queueCount => 
      _highPriorityQueue.length + 
      _mediumPriorityQueue.length + 
      _lowPriorityQueue.length;
  
  /// 根据图片URL获取最佳预加载质量
  ImagePreloadQuality getBestQuality(String url, {bool isVisible = false}) {
    // 基于网络状态和图片可见性决定最佳质量
    if (_isOnWifi) {
      return isVisible 
          ? ImagePreloadQuality.original 
          : ImagePreloadQuality.high;
    } else {
      return isVisible 
          ? ImagePreloadQuality.high 
          : ImagePreloadQuality.medium;
    }
  }
  
  /// 优化内存使用，清理低优先级缓存
  void optimizeMemoryUsage() {
    if (_isLowMemory) {
      // 内存压力大时，清理部分缓存
      int count = 0;
      final keysToRemove = <String>[];
      
      // 找出低优先级的缓存项清除
      for (final entry in _preloadedImages.entries) {
        if (count > 50) { // 保留最多50个缓存项
          keysToRemove.add(entry.key);
        }
        count++;
      }
      
      // 移除低优先级缓存项
      for (final key in keysToRemove) {
        _preloadedImages.remove(key);
      }
      
      // 清空低优先级队列
      _lowPriorityQueue.clear();
      
      LogUtil.debug('内存优化：清理了${keysToRemove.length}个缓存项');
    }
  }
  
  /// 预测并预加载即将需要的图片
  void predictAndPreload(List<String> recentlyAccessedUrls, List<String> potentialNextUrls) {
    // 优先加载最近访问过的图片，可能会再次访问
    preloadImages(
      recentlyAccessedUrls,
      priority: ImagePreloadPriority.medium,
      quality: getBestQuality('', isVisible: false),
    );
    
    // 预加载可能即将访问的图片
    if (_isOnWifi && !_isLowMemory) {
      // 仅在Wifi环境和内存充足时预加载
      preloadImages(
        potentialNextUrls,
        priority: ImagePreloadPriority.low,
        quality: ImagePreloadQuality.medium,
      );
    }
  }
  
  /// 设置网络状态
  void setNetworkStatus(bool isWifi) {
    _isOnWifi = isWifi;
  }
  
  /// 设置内存状态
  void setMemoryStatus(bool isLowMemory) {
    _isLowMemory = isLowMemory;
    if (isLowMemory) {
      optimizeMemoryUsage();
    }
  }

  /// 释放资源
  void dispose() {
    // 取消所有定时器
    _debounceTimer?.cancel();
    _cleanupTimer?.cancel();
    _queueProcessingTimer?.cancel();

    // 清空所有缓存和队列
    _highPriorityQueue.clear();
    _mediumPriorityQueue.clear();
    _lowPriorityQueue.clear();
    _loadingImages.clear();
    _preloadedImages.clear();
    _recentlyProcessed.clear();
    _batchQueue.clear();

    LogUtil.debug('ImagePreloader 资源已释放');
  }
}