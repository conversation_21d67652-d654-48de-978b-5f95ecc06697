import 'package:get/get.dart';
import 'package:rolio/common/state/global_event_state.dart';
import 'package:rolio/common/services/role_provider.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:rolio/modules/role/service/recommend_service.dart';
import 'package:rolio/manager/global_state.dart';

/// 会话绑定服务
///
/// 专门处理角色与会话ID的绑定逻辑，减少不必要的绑定操作
class SessionBindingService extends GetxService {
  // 依赖服务
  final GlobalState _globalState = Get.find<GlobalState>();

  // 记录已绑定的角色-会话对，避免重复绑定
  final Map<int, int> _boundRoleConversations = {};

  // Worker管理
  final List<Worker> _workers = [];

  // 懒加载获取RoleProvider，确保每次都获取最新的实例
  RoleProvider get _roleService => Get.find<RoleProvider>();

  @override
  void onInit() {
    super.onInit();

    // 监听事件总线上的角色会话绑定事件
    _setupEventListeners();

    LogUtil.debug('SessionBindingService初始化完成');
  }

  /// 设置事件监听器
  void _setupEventListeners() {
    // 监听角色会话绑定事件
    _workers.add(ever(GlobalEventState.to.roleConversationBound, (Map<String, dynamic>? data) {
      if (data != null && data.containsKey('role_id') && data.containsKey('conversation_id')) {
        final roleId = int.tryParse(data['role_id'].toString());
        final conversationId = int.tryParse(data['conversation_id'].toString());

        if (roleId != null && conversationId != null && roleId > 0 && conversationId > 0) {
          _processRoleConversationBinding(roleId, conversationId);
        }
      }
    }));
  }
  
  /// 绑定角色到会话ID
  /// 
  /// [roleId] 角色ID
  /// [conversationId] 会话ID
  /// [force] 是否强制绑定，即使已经绑定过
  Future<void> bindRoleToConversation(int roleId, int conversationId, {bool force = false}) async {
    try {
      // 检查角色ID和会话ID是否有效
      if (roleId <= 0 || conversationId <= 0) {
        LogUtil.warn('无效的角色ID或会话ID: roleId=$roleId, conversationId=$conversationId');
        return;
      }
      
      // 检查是否已经绑定过相同的会话
      if (!force && _boundRoleConversations.containsKey(roleId) && 
          _boundRoleConversations[roleId] == conversationId) {
        LogUtil.debug('角色已绑定到相同会话，跳过: roleId=$roleId, conversationId=$conversationId');
        return;
      }
      
      // 处理绑定逻辑
      await _processRoleConversationBinding(roleId, conversationId);
      
      // 发送事件通知
      _notifyBindingComplete(roleId, conversationId);
      
      LogUtil.info('成功绑定角色到会话: roleId=$roleId, conversationId=$conversationId');
    } catch (e) {
      LogUtil.error('绑定角色到会话失败: $e');
    }
  }
  
  /// 处理角色会话绑定逻辑
  Future<void> _processRoleConversationBinding(int roleId, int conversationId) async {
    try {
      // 记录绑定关系
      _boundRoleConversations[roleId] = conversationId;
      
      // 获取最新的RoleProvider实例并更新角色会话ID
      await _roleService.updateRoleConversationId(roleId, conversationId);
      
      // 通知全局状态更新
      _globalState.notifyRoleConversationUpdate(roleId, conversationId);
      
      // 如果有RecommendService，也直接更新其中的角色会话ID
      if (Get.isRegistered<RecommendService>()) {
        try {
          final recommendService = Get.find<RecommendService>();
          // 先尝试获取角色信息
          final role = await _roleService.getRoleById(roleId);
          if (role != null) {
            // 创建更新后的角色对象
            final updatedRole = role.copyWith(conversationId: conversationId);
            // 直接调用RecommendService的方法更新
            await recommendService.updateRoleConversationId(roleId, conversationId, updatedRole);
            LogUtil.debug('已更新RecommendService中角色会话ID: roleId=$roleId, conversationId=$conversationId');
          }
        } catch (e) {
          LogUtil.error('更新RecommendService中角色会话ID失败: $e');
        }
      }
      
      LogUtil.debug('角色会话绑定处理完成: roleId=$roleId, conversationId=$conversationId');
    } catch (e) {
      LogUtil.error('处理角色会话绑定失败: $e');
    }
  }
  
  /// 通知绑定完成
  void _notifyBindingComplete(int roleId, int conversationId) {
    // 注释：roleConversationUpdated事件没有监听者，已移除
    // 如果需要通知绑定完成，可以使用roleConversationBound事件
    LogUtil.debug('角色会话绑定完成: roleId=$roleId, conversationId=$conversationId');
  }
  
  /// 检查角色是否已绑定会话
  /// 
  /// [roleId] 角色ID
  /// 返回是否已绑定
  bool isRoleBound(int roleId) {
    return _boundRoleConversations.containsKey(roleId);
  }
  
  /// 获取角色绑定的会话ID
  /// 
  /// [roleId] 角色ID
  /// 返回会话ID，如果未绑定则返回null
  int? getConversationIdForRole(int roleId) {
    return _boundRoleConversations[roleId];
  }
  
  /// 清除角色绑定
  /// 
  /// [roleId] 角色ID
  void clearBinding(int roleId) {
    _boundRoleConversations.remove(roleId);
    LogUtil.debug('已清除角色绑定: roleId=$roleId');
  }
  
  /// 清除所有绑定
  Future<void> clearAllBindings() async {
    try {
      // 获取所有已绑定的角色ID
      final boundRoleIds = _boundRoleConversations.keys.toList();
      
      // 清除内存中的绑定映射
      _boundRoleConversations.clear();
      
      // 获取最新的RoleProvider实例，并清除所有角色的会话ID
      final roleService = _roleService;
      for (final roleId in boundRoleIds) {
        try {
          await roleService.updateRoleConversationId(roleId, 0);
          LogUtil.debug('已清除角色会话ID: roleId=$roleId');
        } catch (e) {
          LogUtil.error('清除角色会话ID失败: roleId=$roleId, 错误: $e');
        }
      }
      
      // 如果有RecommendService，也清除其中的角色会话ID
      if (Get.isRegistered<RecommendService>()) {
        try {
          final recommendService = Get.find<RecommendService>();
          final roles = recommendService.getRolesList();
          
          for (final role in roles) {
            if (role.conversationId != null && role.conversationId! > 0) {
              final updatedRole = role.copyWith(conversationId: 0);
              await recommendService.updateRoleConversationId(role.id, 0, updatedRole);
              LogUtil.debug('已清除推荐服务中角色会话ID: roleId=${role.id}');
            }
          }
        } catch (e) {
          LogUtil.error('清除推荐服务中角色会话ID失败: $e');
        }
      }
      
      LogUtil.debug('已清除所有角色绑定');
    } catch (e) {
      LogUtil.error('清除所有角色绑定失败: $e');
    }
  }
  
  /// 处理用户状态变化（登录/登出）
  /// 
  /// 在用户登录或登出后调用此方法，以确保数据正确刷新
  /// [refreshRecommendData] 是否刷新推荐页数据 (已在LoginService中处理，此处保留参数兼容现有代码)
  Future<void> handleUserStateChange({bool refreshRecommendData = false}) async {
    try {
      // 先清除所有绑定关系
      await clearAllBindings();
      
      // 不再主动刷新推荐数据，而是依赖LoginService的匿名登录成功后刷新
      // 如果仍需要恢复角色会话绑定，可以从缓存或其他地方获取数据
      LogUtil.info('用户状态变化处理完成，会话绑定已更新');
    } catch (e) {
      LogUtil.error('处理用户状态变化失败: $e');
    }
  }

  @override
  void onClose() {
    // 清理Workers
    for (var worker in _workers) {
      worker.dispose();
    }
    _workers.clear();

    LogUtil.debug('SessionBindingService已清理资源');
    super.onClose();
  }
}