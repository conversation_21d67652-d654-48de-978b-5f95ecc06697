import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rolio/modules/login/service/login_service.dart';
import 'package:rolio/common/utils/logger.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:rolio/common/utils/http_manager_util.dart';
import 'dart:async'; // Added for Timer
import 'package:rolio/manager/global_state.dart'; // GlobalState
import 'package:get/get.dart'; // Added for Worker
import 'package:rolio/common/utils/toast_util.dart';

/// 登录控制器
class LoginController extends GetxController {
  /// 登录服务
  final LoginService _loginService;
  
  /// 全局状态
  final GlobalState _globalState = Get.find<GlobalState>();
  
  /// 构造函数
  LoginController({required LoginService loginService}) : _loginService = loginService;
  
  /// 邮箱输入控制器
  final TextEditingController emailController = TextEditingController();
  
  /// 密码输入控制器
  final TextEditingController passwordController = TextEditingController();
  
  /// 确认密码输入控制器
  final TextEditingController confirmPasswordController = TextEditingController();
  
  /// 是否正在加载
  final RxBool isLoading = false.obs;
  
  /// 是否显示密码
  final RxBool showPassword = false.obs;
  
  /// 是否显示确认密码
  final RxBool showConfirmPassword = false.obs;
  
  /// 是否是注册模式
  final RxBool isRegisterMode = false.obs;
  
  /// 用户状态信息
  final Rx<Map<String, dynamic>> userStatus = Rx<Map<String, dynamic>>({});
  
  // 防抖计时器
  Timer? _debounceTimer;
  
  // 最后一次登录/注册尝试的时间
  DateTime _lastLoginAttempt = DateTime.now().subtract(const Duration(seconds: 5));
  
  // 防抖时间 (毫秒)，设置为较短的值
  static const int _debounceTime = 200;
  
  // 工作线程，用于监听全局认证状态
  Worker? _authProcessingWorker;
  
  @override
  void onInit() {
    super.onInit();
    _checkCurrentUser();
    _updateUserStatus();
    
    // 设置对全局认证状态的监听
    _setupAuthProcessingListener();
    
    // 如果当前在注册模式，检查用户是否可以升级
    if (isRegisterMode.value && !userStatus.value['canUpgrade']) {
      ToastUtil.warning('You need to log out first to register a new account');
    }
  }
  
  /// 设置对全局认证状态的监听
  void _setupAuthProcessingListener() {
    _authProcessingWorker = ever(_globalState.isAuthProcessing, (bool processing) {
      LogUtil.debug('全局认证状态变更: ${processing ? "处理中" : "已完成"}');
      isLoading.value = processing;
    });
  }
  
  /// 检查当前用户并设置token
  void _checkCurrentUser() {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser != null) {
      _updateUserToken(currentUser);
    }
  }
  
  /// 更新用户状态信息
  void _updateUserStatus() {
    userStatus.value = _loginService.checkUserUpgradeStatus();
  }
  
  /// 更新用户token
  Future<void> _updateUserToken(User user) async {
    try {
      final token = await user.getIdToken();
      if (token != null) {
        await HttpManager.setTokenAsync(token);
        LogUtil.debug('用户token已更新');
      }
    } catch (e) {
      LogUtil.error('获取用户token失败: $e');
    }
  }
  
  @override
  void onClose() {
    emailController.dispose();
    passwordController.dispose();
    confirmPasswordController.dispose();
    _debounceTimer?.cancel();
    _authProcessingWorker?.dispose();
    super.onClose();
  }
  
  /// 切换登录/注册模式
  void toggleMode() {
    isRegisterMode.value = !isRegisterMode.value;
    // 清空输入框
    if (isRegisterMode.value) {
      // 如果切换到注册模式，保留邮箱
      passwordController.clear();
      confirmPasswordController.clear();

      
      // 检查当前用户是否可以升级
      _updateUserStatus();
      if (!userStatus.value['canUpgrade']) {
        // 如果当前用户不是游客，提示用户先登出
        ToastUtil.warning('You need to log out first to register a new account');
      }
    } else {
      // 如果切换到登录模式，保留邮箱，清空其他
      passwordController.clear();
      confirmPasswordController.clear();

    }
  }
  
  /// 切换密码可见性
  void togglePasswordVisibility() {
    showPassword.value = !showPassword.value;
  }
  
  /// 切换确认密码可见性
  void toggleConfirmPasswordVisibility() {
    showConfirmPassword.value = !showConfirmPassword.value;
  }
  
  /// 邮箱密码登录(带防抖)
  Future<void> loginWithEmailPassword() async {
    // 防止连续多次点击，使用较短的防抖时间
    if (_debounceTimer?.isActive ?? false) {
      LogUtil.debug('登录操作被防抖机制阻止');
      return;
    }
    
    // 确保不会频繁尝试登录
    final now = DateTime.now();
    if (now.difference(_lastLoginAttempt).inMilliseconds < _debounceTime) {
      LogUtil.debug('登录操作过于频繁，被阻止');
      return;
    }
    _lastLoginAttempt = now;
    
    // 设置防抖计时器
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: _debounceTime), () {
      // 防抖时间结束后的操作（空操作，只是用于防抖）
    });
    
    final email = emailController.text.trim();
    final password = passwordController.text;
    
    // 验证输入
    if (email.isEmpty) {
      ToastUtil.error('Please enter email address');
      return;
    }
    
    if (password.isEmpty) {
      ToastUtil.error('Please enter password');
      return;
    }
    
    try {
      isLoading.value = true;
      final userCredential = await _loginService.loginWithEmailPassword(email, password);
      
      // 设置用户token
      if (userCredential.user != null) {
        await _updateUserToken(userCredential.user!);
      }
      
      // 登录成功后返回
      Get.back();
      ToastUtil.success('Login successful');
    } catch (e) {
      LogUtil.error('邮箱密码登录失败: $e');
      // 错误处理已在服务层完成
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 游客账号升级为正式账号(带防抖)
  Future<void> registerAccount() async {
    // 防止连续多次点击，使用较短的防抖时间
    if (_debounceTimer?.isActive ?? false) {
      LogUtil.debug('注册操作被防抖机制阻止');
      return;
    }
    
    // 确保不会频繁尝试注册
    final now = DateTime.now();
    if (now.difference(_lastLoginAttempt).inMilliseconds < _debounceTime) {
      LogUtil.debug('注册操作过于频繁，被阻止');
      return;
    }
    _lastLoginAttempt = now;
    
    // 设置防抖计时器
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: _debounceTime), () {
      // 防抖时间结束后的操作（空操作，只是用于防抖）
    });
    
    final email = emailController.text.trim();
    final password = passwordController.text;
    final confirmPassword = confirmPasswordController.text;
    
    // 验证输入
    if (email.isEmpty) {
      ToastUtil.error('Please enter email address');
      return;
    }
    
    if (password.isEmpty) {
      ToastUtil.error('Please enter password');
      return;
    }
    
    if (password != confirmPassword) {
      ToastUtil.error('Passwords do not match');
      return;
    }
    
    if (password.length < 6) {
      ToastUtil.error('Password must be at least 6 characters');
      return;
    }
    
    try {
      isLoading.value = true;
      
      // 检查当前用户状态
      _updateUserStatus();
      if (!userStatus.value['canUpgrade']) {
        ToastUtil.error('Current user cannot register. Please log out first.');
        isLoading.value = false;
        return;
      }
      
      final credential = await _loginService.upgradeAnonymousUserWithEmail(email, password);
      
      // 设置用户token
      if (credential.user != null) {
        await _updateUserToken(credential.user!);
      }
      
      // 注册成功后返回
      Get.back();
      ToastUtil.success('Registration successful');
    } catch (e) {
      LogUtil.error('游客账号升级失败: $e');
      // 错误处理已在服务层完成
    } finally {
      isLoading.value = false;
    }
  }
  
  /// 退出登录
  Future<void> logout() async {
    try {
      await _loginService.logoutUser();
      // 清除token
      await HttpManager.clearTokenAsync();
      ToastUtil.success('已退出登录');
    } catch (e) {
      LogUtil.error('退出登录失败: $e');
      ToastUtil.error('退出登录失败: ${e.toString()}');
    }
  }
}
