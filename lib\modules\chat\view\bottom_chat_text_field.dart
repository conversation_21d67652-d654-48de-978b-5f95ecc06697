import 'dart:io';
import 'dart:async'; // 添加Timer导入
import 'package:emoji_picker_flutter/emoji_picker_flutter.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:rolio/common/constants/colors_constants.dart';
import 'package:rolio/common/utils/toast_util.dart';
import 'package:rolio/modules/chat/model/message.dart';
import '../controller/chat_controller.dart';
import 'chat_widgets.dart';

class BottomChatTextField extends StatefulWidget {
  const BottomChatTextField({
    Key? key,
    required this.receiverUserId,
    required this.isGroupChat,
  }) : super(key: key);

  final String receiverUserId;
  final bool isGroupChat;

  @override
  State<BottomChatTextField> createState() => _BottomChatTextFieldState();
}

class _BottomChatTextFieldState extends State<BottomChatTextField> {
  late final TextEditingController _messageController;
  late final FocusNode _tfFocusNode;
  bool _isEmojiIconTapped = false;
  double? _keyboardSize;
  
  // 添加状态变量，用于跟踪是否处于等待回复状态
  final RxBool _isWaitingForResponse = false.obs;
  
  // 输入框提示文本
  final RxString _inputHintText = 'Send message...'.obs;

  final ChatController chatController = Get.find<ChatController>();
  
  // 添加防抖相关变量
  DateTime _lastMessageTime = DateTime.now().subtract(const Duration(seconds: 2));
  Timer? _debounceTimer;
  static const int _debounceMilliseconds = 700; // 防抖时间，单位毫秒
  bool _isProcessingSend = false; // 是否正在处理发送

  @override
  void initState() {
    super.initState();
    _messageController = TextEditingController();
    _tfFocusNode = FocusNode();
    
    // 初始化时检查是否有待处理消息
    _checkWaitingState();
    
    // 添加AI回复状态监听
    _setupAiReplyingListener();
  }
  
  // 设置AI回复状态监听
  void _setupAiReplyingListener() {
    // 使用ever监听isAiReplying变化
    ever(chatController.isAiReplying, (bool isReplying) {
      _updateWaitingState(isReplying);
    });
    
    // 立即更新一次状态
    _updateWaitingState(chatController.isAiReplying.value);
  }
  
  // 更新等待状态
  void _updateWaitingState(bool isWaiting) {
    _isWaitingForResponse.value = isWaiting;
    
    // 更新提示文本
    if (isWaiting) {
      _inputHintText.value = 'Please wait...';
      // 不再清空输入框内容和取消焦点，以保持键盘展开状态
      // _messageController.clear();
      // _tfFocusNode.unfocus();
      
      // 确保表情选择器关闭
      if (_isEmojiIconTapped) {
        setState(() {
          _isEmojiIconTapped = false;
        });
      }
    } else {
      _inputHintText.value = 'Send message...';
    }
  }
  
  // 检查是否有待处理消息
  void _checkWaitingState() async {
    bool hasPending = false;
    
    try {
      hasPending = chatController.hasPendingMessages();
    } catch (e) {
      // 忽略错误
    }
    
    _updateWaitingState(hasPending || chatController.isAiReplying.value);
  }

  @override
  void dispose() {
    _messageController.dispose();
    _tfFocusNode.dispose();
    // 取消防抖计时器
    _debounceTimer?.cancel();
    super.dispose();
  }
  
  // 根据状态获取按钮颜色
  Color _getButtonColor() {
    if (_isWaitingForResponse.value || _isProcessingSend) {
      return Colors.grey[700]!; // 等待回复或正在处理发送时使用灰色
    } else if (_messageController.text.trim().isNotEmpty) {
      return AppColors.primary; // 有内容时使用主色
    } else {
      return Colors.grey[800]!; // 无内容时使用深灰色
    }
  }
  
  // 检查是否可以发送消息 (防抖和状态检查)
  bool _canSendMessage() {
    if (_messageController.text.trim().isEmpty) return false;
    if (_isWaitingForResponse.value) return false;
    if (_isProcessingSend) return false;
    
    // 时间戳防抖检查
    final now = DateTime.now();
    if (now.difference(_lastMessageTime).inMilliseconds < _debounceMilliseconds) {
      print('消息发送过于频繁，被防抖机制阻止');
      return false;
    }
    
    return true;
  }

  @override
  Widget build(BuildContext context) {
    final viewInsets = MediaQuery.of(context).viewInsets;
    if (_keyboardSize == null && viewInsets.bottom > 1) {
      _keyboardSize = viewInsets.bottom;
    }

    // 不再使用外层的Obx，改为使用常规Widget树
    return Container(
      padding: const EdgeInsets.only(left: 16.0, right: 16.0, top: 8.0, bottom: 40.0), // 增加底部安全距离
      color: Colors.transparent,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 回复消息预览 - 单独用Obx包装
          Obx(() => replyMessageProvider.value != null 
            ? const ReplyMessagePreview() 
            : const SizedBox.shrink()),
          
          // 新的输入框设计 - 灰色圆角
          Container(
            decoration: BoxDecoration(
              color: const Color(0xFF686868), // 灰色背景
              borderRadius: BorderRadius.circular(50.0), // 大圆角
            ),
            height: 50,
            child: Row(
              children: [
                // 左侧表情按钮 - 恢复原有图标和逻辑
                Padding(
                  padding: const EdgeInsets.only(left: 12.0),
                  child: Obx(() {
                    final isWaiting = _isWaitingForResponse.value;
                    return IconButton(
                      onPressed: () {
                        setState(() {
                          _isEmojiIconTapped = !_isEmojiIconTapped;
                          if (_isEmojiIconTapped) {
                            _tfFocusNode.unfocus();
                          }
                        });
                      },
                      icon: Icon(
                        Icons.emoji_emotions_outlined,
                        // 在等待响应时使用灰色
                        color: isWaiting 
                          ? Colors.grey[600] 
                          : (_isEmojiIconTapped ? AppColors.primary : Colors.white70),
                        size: 24,
                      ),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(
                        minWidth: 36,
                        minHeight: 36,
                      ),
                    );
                  }),
                ),
                
                // 文本输入框
                Expanded(
                  child: Obx(() {
                    final hintText = _inputHintText.value;
                    final isWaiting = _isWaitingForResponse.value;
                    
                    return TextField(
                      controller: _messageController,
                      focusNode: _tfFocusNode,
                      maxLines: 1,
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.white,
                      ),
                      decoration: InputDecoration(
                        hintText: hintText,
                        hintStyle: TextStyle(
                          color: isWaiting 
                            ? Colors.grey[400] 
                            : Colors.white70,
                          fontStyle: isWaiting 
                            ? FontStyle.italic 
                            : FontStyle.normal,
                        ),
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.symmetric(vertical: 12.0, horizontal: 12.0),
                      ),
                      onChanged: (_) {
                        setState(() {});
                      },
                    );
                  }),
                ),
                
                // 右侧发送按钮 - 恢复原有图标和逻辑
                Padding(
                  padding: const EdgeInsets.only(right: 8.0),
                  child: GestureDetector(
                    onTap: () {
                      if (_canSendMessage()) {
                        _sendTextMessage();
                      }
                    },
                    child: Obx(() {
                      // 获取UI状态 - 包含防抖状态
                      final bool isProcessing = _isProcessingSend || _isWaitingForResponse.value;
                      final bool hasText = _messageController.text.trim().isNotEmpty;
                      
                      return Container(
                        height: 40,
                        width: 40,
                        decoration: BoxDecoration(
                          color: _getButtonColor(),
                          shape: BoxShape.circle,
                        ),
                        child: Center(
                          child: isProcessing
                              ? Icon(
                                  Icons.hourglass_empty,
                                  color: Colors.grey[300],
                                  size: 20,
                                )
                              : Icon(
                                  Icons.send,
                                  color: hasText
                                      ? Colors.white
                                      : Colors.grey[600],
                                  size: 20,
                                ),
                        ),
                      );
                    }),
                  ),
                ),
              ],
            ),
          ),
          
          // 表情选择器
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            height: _isEmojiIconTapped ? _keyboardSize ?? 310 : 0.0,
            width: double.infinity,
            child: EmojiPicker(
              textEditingController: _messageController,
              onEmojiSelected: (_, __) => setState(() {}),
            ),
          ),
        ],
      ),
    );
  }

  void _sendTextMessage() async {
    // 使用防抖和状态检查
    if (!_canSendMessage()) return;
    
    // 设置防抖状态
    _isProcessingSend = true;
    _lastMessageTime = DateTime.now();
    
    // 取消之前的防抖计时器
    _debounceTimer?.cancel();
    
    // 执行一次状态检查，确保UI已更新
    await _checkAndUpdateWaitingState();
    
    // 如果状态检查后发现正在等待回复，则直接返回
    if (_isWaitingForResponse.value) {
      _isProcessingSend = false;
      return;
    }

    // 安全检查：保存消息内容，避免在异步操作中使用已释放的控制器
    final content = _messageController.text.trim();
    
    // 先清空输入框
    try {
      // 添加安全检查，防止使用已释放的控制器
      if (_messageController.hasListeners) {
        _messageController.clear();
      }
    } catch (e) {
      // 如果控制器已被释放，忽略错误
      print('清空输入框失败: $e');
    }
    
    try {
      // 发送消息
      await chatController.sendTextMessage(
        lastMessage: content,
        receiverUserId: widget.receiverUserId,
        groupId: null,
        isGroupChat: widget.isGroupChat,
      );
      
      // 更新等待状态 - 设置为正在等待
      _updateWaitingState(true);
    } catch (e) {
      ToastUtil.error('Failed to send message, please try again');
      // 失败时重置等待状态
      _updateWaitingState(false);
    } finally {
      // 设置防抖计时器，防止快速连续点击
      _debounceTimer = Timer(Duration(milliseconds: _debounceMilliseconds), () {
        _isProcessingSend = false;
      });
    }
    
    // 清除回复消息状态
    if (replyMessageProvider.value != null) {
      replyMessageProvider.value = null;
    }
  }
  
  // 检查是否有待处理消息
  Future<bool> _checkPendingMessages() async {
    try {
      // 使用控制器检查是否有待处理消息
      // 首先尝试使用专门的方法(如果存在)
      try {
        // 调用控制器的方法检查是否有待处理消息
        return chatController.hasPendingMessages();
      } catch (e) {
        // 如果该方法不存在，则使用AI回复状态作为替代
        return chatController.isAiReplying.value;
      }
    } catch (e) {
      print('Error checking pending messages: $e');
      // 出错时，默认允许发送
      return false;
    }
  }
  
  // 检查并更新等待状态
  Future<void> _checkAndUpdateWaitingState() async {
    try {
      // 获取是否有待处理消息
      bool hasPending = await _checkPendingMessages();
      // 获取AI是否正在回复
      bool isReplying = chatController.isAiReplying.value;
      
      // 更新等待状态
      _updateWaitingState(hasPending || isReplying);
    } catch (e) {
      print('Error updating waiting state: $e');
    }
  }
}
